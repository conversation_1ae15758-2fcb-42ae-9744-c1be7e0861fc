{"doc": "\n 岗位信息 数据层\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectPagePostList", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": "\n 分页查询岗位列表\r\n\r\n @param page         分页对象\r\n @param queryWrapper 查询条件\r\n @return 包含岗位信息的分页结果\r\n"}, {"name": "selectPostsByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 查询用户所属岗位组\r\n\r\n @param userId 用户ID\r\n @return 结果\r\n"}], "constructors": []}