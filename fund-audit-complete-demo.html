<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资金审计管理系统 - 完整Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            height: 100vh;
            overflow: hidden;
        }
        
        .app-container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            color: white;
            overflow-y: auto;
        }
        
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #34495e;
        }
        
        .sidebar-header h1 {
            font-size: 18px;
            margin-bottom: 5px;
        }
        
        .sidebar-header p {
            font-size: 12px;
            opacity: 0.8;
        }
        
        .nav-menu {
            padding: 20px 0;
        }
        
        .nav-group {
            margin-bottom: 20px;
        }
        
        .nav-group-title {
            padding: 10px 20px;
            font-size: 12px;
            color: #bdc3c7;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .nav-item {
            display: block;
            padding: 12px 20px;
            color: #ecf0f1;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        
        .nav-item:hover {
            background: rgba(255,255,255,0.1);
            border-left-color: #3498db;
        }
        
        .nav-item.active {
            background: rgba(52, 152, 219, 0.2);
            border-left-color: #3498db;
            color: #3498db;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .header {
            background: white;
            padding: 15px 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h2 {
            color: #2c3e50;
            font-size: 20px;
        }
        
        .header-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .content-area {
            flex: 1;
            padding: 20px 30px;
            overflow-y: auto;
            background: #f8f9fa;
        }
        
        .page {
            display: none;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            min-height: calc(100vh - 140px);
        }
        
        .page.active {
            display: block;
        }
        
        .page-content {
            padding: 20px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
            margin-top: 20px;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        
        .data-table tr:hover {
            background: #f8f9fa;
        }
        
        .amount {
            text-align: right;
            font-family: 'Consolas', monospace;
            font-weight: 500;
        }
        
        .amount.positive {
            color: #27ae60;
        }
        
        .amount.negative {
            color: #e74c3c;
        }
        
        .amount.frozen {
            color: #f39c12;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #2c3e50;
        }
        
        .form-control {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }
        
        .form-row {
            display: flex;
            gap: 20px;
        }
        
        .form-col {
            flex: 1;
        }
        
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .stats-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stats-card h3 {
            color: #7f8c8d;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .stats-card .value {
            font-size: 24px;
            font-weight: 600;
            font-family: 'Consolas', monospace;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-approved {
            background: #d4edda;
            color: #155724;
        }
        
        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }
        
        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        
        .search-box {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .search-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            width: 250px;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏导航 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h1>资金审计系统</h1>
                <p>Fund Audit Management</p>
            </div>
            
            <nav class="nav-menu">
                <div class="nav-group">
                    <div class="nav-group-title">余额看板</div>
                    <a href="#" class="nav-item active" onclick="showPage('bank-balance')">银行账户余额</a>
                    <a href="#" class="nav-item" onclick="showPage('dept-balance')">施工部余额</a>
                    <a href="#" class="nav-item" onclick="showPage('project-balance')">施工项目余额</a>
                    <a href="#" class="nav-item" onclick="showPage('regional-balance')">异地区域项目余额</a>
                </div>
                
                <div class="nav-group">
                    <div class="nav-group-title">业务操作</div>
                    <a href="#" class="nav-item" onclick="showPage('account-allocation')">账户分配</a>
                    <a href="#" class="nav-item" onclick="showPage('planned-payment')">计划支付</a>
                    <a href="#" class="nav-item" onclick="showPage('dept-lending')">施工部拆借</a>
                    <a href="#" class="nav-item" onclick="showPage('account-freeze')">账户冻结</a>
                </div>
                
                <div class="nav-group">
                    <div class="nav-group-title">数据录入</div>
                    <a href="#" class="nav-item" onclick="showPage('bank-journal')">招行0801（银行日记）</a>
                </div>
            </nav>
        </div>
        
        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="header">
                <h2 id="page-title">银行账户余额</h2>
                <div class="header-actions">
                    <button class="btn btn-primary">导出Excel</button>
                    <button class="btn btn-success">刷新数据</button>
                </div>
            </div>
            
            <div class="content-area">
                <!-- 银行账户余额页面 -->
                <div class="page active" id="bank-balance">
                    <div class="page-content">
                        <div class="stats-cards">
                            <div class="stats-card">
                                <h3>银行账户总数</h3>
                                <div class="value" style="color: #3498db;">15</div>
                            </div>
                            <div class="stats-card">
                                <h3>总余额</h3>
                                <div class="value amount positive">¥12,458,900</div>
                            </div>
                            <div class="stats-card">
                                <h3>民工专户余额</h3>
                                <div class="value amount positive">¥8,756,200</div>
                            </div>
                            <div class="stats-card">
                                <h3>一般户余额</h3>
                                <div class="value amount positive">¥3,702,700</div>
                            </div>
                        </div>
                        
                        <div class="toolbar">
                            <div class="search-box">
                                <input type="text" class="search-input" placeholder="搜索银行账户...">
                                <button class="btn btn-primary">搜索</button>
                            </div>
                            <div class="action-buttons">
                                <button class="btn btn-success">新增账户</button>
                                <button class="btn btn-primary">批量导入</button>
                            </div>
                        </div>
                        
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>银行名称</th>
                                    <th>账户号码</th>
                                    <th>账户性质</th>
                                    <th>账户余额</th>
                                    <th>制单金额</th>
                                    <th>可用余额</th>
                                    <th>最后更新</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="bank-balance-table">
                                <!-- 数据将通过JavaScript生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- 施工部余额页面 -->
                <div class="page" id="dept-balance">
                    <div class="page-content">
                        <div class="stats-cards">
                            <div class="stats-card">
                                <h3>施工部总数</h3>
                                <div class="value" style="color: #3498db;">8</div>
                            </div>
                            <div class="stats-card">
                                <h3>总制单金额</h3>
                                <div class="value amount frozen">¥2,458,900</div>
                            </div>
                            <div class="stats-card">
                                <h3>总银行余额</h3>
                                <div class="value amount positive">¥8,756,200</div>
                            </div>
                            <div class="stats-card">
                                <h3>总活钱金额</h3>
                                <div class="value amount positive">¥5,234,100</div>
                            </div>
                        </div>

                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>施工部名称</th>
                                    <th>制单金额</th>
                                    <th>银行余额</th>
                                    <th>活钱金额</th>
                                    <th>冻结金额</th>
                                    <th>项目数量</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>二部</td>
                                    <td><span class="amount frozen">¥1,138,900</span></td>
                                    <td><span class="amount positive">¥3,756,200</span></td>
                                    <td><span class="amount positive">¥2,034,100</span></td>
                                    <td><span class="amount frozen">¥534,500</span></td>
                                    <td>5</td>
                                    <td><button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">详情</button></td>
                                </tr>
                                <tr>
                                    <td>三部</td>
                                    <td><span class="amount frozen">¥680,000</span></td>
                                    <td><span class="amount positive">¥2,100,000</span></td>
                                    <td><span class="amount positive">¥1,200,000</span></td>
                                    <td><span class="amount frozen">¥300,000</span></td>
                                    <td>3</td>
                                    <td><button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">详情</button></td>
                                </tr>
                                <tr>
                                    <td>四部</td>
                                    <td><span class="amount frozen">¥640,000</span></td>
                                    <td><span class="amount positive">¥2,900,000</span></td>
                                    <td><span class="amount positive">¥2,000,000</span></td>
                                    <td><span class="amount frozen">¥400,000</span></td>
                                    <td>4</td>
                                    <td><button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">详情</button></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 施工项目余额页面 -->
                <div class="page" id="project-balance">
                    <div class="page-content">
                        <div class="toolbar">
                            <div class="search-box">
                                <select class="form-control" style="width: 150px;">
                                    <option>全部施工部</option>
                                    <option>二部</option>
                                    <option>三部</option>
                                    <option>四部</option>
                                </select>
                                <input type="text" class="search-input" placeholder="搜索项目名称...">
                                <button class="btn btn-primary">搜索</button>
                            </div>
                        </div>

                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>施工部</th>
                                    <th>项目名称</th>
                                    <th>制单金额</th>
                                    <th>银行余额</th>
                                    <th>活钱金额</th>
                                    <th>冻结金额</th>
                                    <th>项目状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>二部</td>
                                    <td>郦城一期C3（二部）</td>
                                    <td><span class="amount frozen">¥458,900</span></td>
                                    <td><span class="amount positive">¥1,256,200</span></td>
                                    <td><span class="amount positive">¥834,100</span></td>
                                    <td><span class="amount frozen">¥234,500</span></td>
                                    <td><span class="status-badge status-approved">进行中</span></td>
                                    <td><button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">详情</button></td>
                                </tr>
                                <tr>
                                    <td>二部</td>
                                    <td>西晶项目</td>
                                    <td><span class="amount frozen">¥680,000</span></td>
                                    <td><span class="amount positive">¥2,100,000</span></td>
                                    <td><span class="amount positive">¥1,200,000</span></td>
                                    <td><span class="amount frozen">¥300,000</span></td>
                                    <td><span class="status-badge status-approved">进行中</span></td>
                                    <td><button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">详情</button></td>
                                </tr>
                                <tr>
                                    <td>二部</td>
                                    <td>城南（眉管委）</td>
                                    <td><span class="amount frozen">¥320,000</span></td>
                                    <td><span class="amount positive">¥800,000</span></td>
                                    <td><span class="amount positive">¥600,000</span></td>
                                    <td><span class="amount frozen">¥150,000</span></td>
                                    <td><span class="status-badge status-pending">筹备中</span></td>
                                    <td><button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">详情</button></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 异地区域项目余额页面 -->
                <div class="page" id="regional-balance">
                    <div class="page-content">
                        <div class="stats-cards">
                            <div class="stats-card">
                                <h3>区域总数</h3>
                                <div class="value" style="color: #3498db;">6</div>
                            </div>
                            <div class="stats-card">
                                <h3>异地项目数</h3>
                                <div class="value" style="color: #e67e22;">12</div>
                            </div>
                            <div class="stats-card">
                                <h3>异地总投资</h3>
                                <div class="value amount positive">¥15,680,000</div>
                            </div>
                            <div class="stats-card">
                                <h3>异地总余额</h3>
                                <div class="value amount positive">¥8,420,000</div>
                            </div>
                        </div>

                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>区域名称</th>
                                    <th>项目数量</th>
                                    <th>制单金额</th>
                                    <th>银行余额</th>
                                    <th>活钱金额</th>
                                    <th>冻结金额</th>
                                    <th>负责人</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>眉山片区</td>
                                    <td>4</td>
                                    <td><span class="amount frozen">¥1,200,000</span></td>
                                    <td><span class="amount positive">¥3,500,000</span></td>
                                    <td><span class="amount positive">¥2,100,000</span></td>
                                    <td><span class="amount frozen">¥600,000</span></td>
                                    <td>张经理</td>
                                    <td><button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">详情</button></td>
                                </tr>
                                <tr>
                                    <td>成都片区</td>
                                    <td>3</td>
                                    <td><span class="amount frozen">¥800,000</span></td>
                                    <td><span class="amount positive">¥2,200,000</span></td>
                                    <td><span class="amount positive">¥1,500,000</span></td>
                                    <td><span class="amount frozen">¥400,000</span></td>
                                    <td>李经理</td>
                                    <td><button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">详情</button></td>
                                </tr>
                                <tr>
                                    <td>重庆片区</td>
                                    <td>5</td>
                                    <td><span class="amount frozen">¥1,500,000</span></td>
                                    <td><span class="amount positive">¥2,720,000</span></td>
                                    <td><span class="amount positive">¥1,800,000</span></td>
                                    <td><span class="amount frozen">¥750,000</span></td>
                                    <td>王经理</td>
                                    <td><button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">详情</button></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- 账户分配页面 -->
                <div class="page" id="account-allocation">
                    <div class="page-content">
                        <div class="toolbar">
                            <div class="search-box">
                                <select class="form-control" style="width: 150px;">
                                    <option>全部状态</option>
                                    <option>待分配</option>
                                    <option>已分配</option>
                                    <option>部分分配</option>
                                </select>
                                <input type="text" class="search-input" placeholder="搜索流水摘要...">
                                <button class="btn btn-primary">搜索</button>
                            </div>
                            <div class="action-buttons">
                                <button class="btn btn-success">批量分配</button>
                            </div>
                        </div>

                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th><input type="checkbox"></th>
                                    <th>交易日期</th>
                                    <th>银行账户</th>
                                    <th>交易摘要</th>
                                    <th>收入金额</th>
                                    <th>支出金额</th>
                                    <th>已分配金额</th>
                                    <th>未分配金额</th>
                                    <th>分配状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><input type="checkbox"></td>
                                    <td>2024-01-15</td>
                                    <td>招行0801</td>
                                    <td>郦城项目工程款</td>
                                    <td><span class="amount positive">¥2,000,000</span></td>
                                    <td>-</td>
                                    <td><span class="amount positive">¥1,500,000</span></td>
                                    <td><span class="amount frozen">¥500,000</span></td>
                                    <td><span class="status-badge status-pending">部分分配</span></td>
                                    <td><button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">分配</button></td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox"></td>
                                    <td>2024-01-14</td>
                                    <td>建行民工专户</td>
                                    <td>材料款支付</td>
                                    <td>-</td>
                                    <td><span class="amount negative">¥800,000</span></td>
                                    <td><span class="amount negative">¥800,000</span></td>
                                    <td>¥0</td>
                                    <td><span class="status-badge status-approved">已分配</span></td>
                                    <td><button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">查看</button></td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox"></td>
                                    <td>2024-01-13</td>
                                    <td>工行一般户</td>
                                    <td>预付款收入</td>
                                    <td><span class="amount positive">¥1,200,000</span></td>
                                    <td>-</td>
                                    <td>¥0</td>
                                    <td><span class="amount frozen">¥1,200,000</span></td>
                                    <td><span class="status-badge status-rejected">待分配</span></td>
                                    <td><button class="btn btn-success" style="padding: 4px 8px; font-size: 12px;">分配</button></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 计划支付页面 -->
                <div class="page" id="planned-payment">
                    <div class="page-content">
                        <div class="toolbar">
                            <div class="search-box">
                                <select class="form-control" style="width: 150px;">
                                    <option>全部状态</option>
                                    <option>待审批</option>
                                    <option>已审批</option>
                                    <option>已支付</option>
                                    <option>已拒绝</option>
                                </select>
                                <input type="text" class="search-input" placeholder="搜索支付事由...">
                                <button class="btn btn-primary">搜索</button>
                            </div>
                            <div class="action-buttons">
                                <button class="btn btn-success">新建制单</button>
                                <button class="btn btn-primary">批量审批</button>
                            </div>
                        </div>

                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th><input type="checkbox"></th>
                                    <th>制单日期</th>
                                    <th>制单人</th>
                                    <th>支付事由</th>
                                    <th>施工部</th>
                                    <th>项目名称</th>
                                    <th>计划金额</th>
                                    <th>实付金额</th>
                                    <th>支付状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><input type="checkbox"></td>
                                    <td>2024-01-15</td>
                                    <td>张会计</td>
                                    <td>钢筋材料款</td>
                                    <td>二部</td>
                                    <td>郦城一期C3</td>
                                    <td><span class="amount frozen">¥458,900</span></td>
                                    <td>-</td>
                                    <td><span class="status-badge status-pending">待审批</span></td>
                                    <td>
                                        <button class="btn btn-success" style="padding: 4px 8px; font-size: 12px;">审批</button>
                                        <button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">编辑</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox"></td>
                                    <td>2024-01-14</td>
                                    <td>李会计</td>
                                    <td>人工费支付</td>
                                    <td>三部</td>
                                    <td>西晶项目</td>
                                    <td><span class="amount frozen">¥680,000</span></td>
                                    <td><span class="amount positive">¥680,000</span></td>
                                    <td><span class="status-badge status-approved">已支付</span></td>
                                    <td><button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">查看</button></td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox"></td>
                                    <td>2024-01-13</td>
                                    <td>王会计</td>
                                    <td>设备租赁费</td>
                                    <td>四部</td>
                                    <td>城南项目</td>
                                    <td><span class="amount frozen">¥320,000</span></td>
                                    <td>-</td>
                                    <td><span class="status-badge status-approved">已审批</span></td>
                                    <td>
                                        <button class="btn btn-success" style="padding: 4px 8px; font-size: 12px;">支付</button>
                                        <button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">查看</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 施工部拆借页面 -->
                <div class="page" id="dept-lending">
                    <div class="page-content">
                        <div class="stats-cards">
                            <div class="stats-card">
                                <h3>本月拆借笔数</h3>
                                <div class="value" style="color: #3498db;">15</div>
                            </div>
                            <div class="stats-card">
                                <h3>拆借总金额</h3>
                                <div class="value amount positive">¥3,200,000</div>
                            </div>
                            <div class="stats-card">
                                <h3>待还金额</h3>
                                <div class="value amount frozen">¥1,800,000</div>
                            </div>
                            <div class="stats-card">
                                <h3>逾期金额</h3>
                                <div class="value amount negative">¥200,000</div>
                            </div>
                        </div>

                        <div class="toolbar">
                            <div class="search-box">
                                <select class="form-control" style="width: 150px;">
                                    <option>全部状态</option>
                                    <option>拆借中</option>
                                    <option>已归还</option>
                                    <option>逾期</option>
                                </select>
                                <input type="text" class="search-input" placeholder="搜索拆借事由...">
                                <button class="btn btn-primary">搜索</button>
                            </div>
                            <div class="action-buttons">
                                <button class="btn btn-success">新建拆借</button>
                            </div>
                        </div>

                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>拆借日期</th>
                                    <th>拆出部门</th>
                                    <th>拆入部门</th>
                                    <th>拆借事由</th>
                                    <th>拆借金额</th>
                                    <th>约定归还日期</th>
                                    <th>已归还金额</th>
                                    <th>剩余金额</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>2024-01-10</td>
                                    <td>二部</td>
                                    <td>三部</td>
                                    <td>临时资金周转</td>
                                    <td><span class="amount positive">¥500,000</span></td>
                                    <td>2024-01-25</td>
                                    <td><span class="amount positive">¥200,000</span></td>
                                    <td><span class="amount frozen">¥300,000</span></td>
                                    <td><span class="status-badge status-pending">拆借中</span></td>
                                    <td>
                                        <button class="btn btn-success" style="padding: 4px 8px; font-size: 12px;">归还</button>
                                        <button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">详情</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2024-01-05</td>
                                    <td>四部</td>
                                    <td>二部</td>
                                    <td>设备采购资金</td>
                                    <td><span class="amount positive">¥800,000</span></td>
                                    <td>2024-01-20</td>
                                    <td><span class="amount positive">¥800,000</span></td>
                                    <td>¥0</td>
                                    <td><span class="status-badge status-approved">已归还</span></td>
                                    <td><button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">详情</button></td>
                                </tr>
                                <tr>
                                    <td>2023-12-28</td>
                                    <td>三部</td>
                                    <td>四部</td>
                                    <td>年底资金紧张</td>
                                    <td><span class="amount positive">¥200,000</span></td>
                                    <td>2024-01-10</td>
                                    <td>¥0</td>
                                    <td><span class="amount negative">¥200,000</span></td>
                                    <td><span class="status-badge status-rejected">逾期</span></td>
                                    <td>
                                        <button class="btn btn-success" style="padding: 4px 8px; font-size: 12px;">催收</button>
                                        <button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">详情</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 账户冻结页面 -->
                <div class="page" id="account-freeze">
                    <div class="page-content">
                        <div class="toolbar">
                            <div class="search-box">
                                <select class="form-control" style="width: 150px;">
                                    <option>全部状态</option>
                                    <option>冻结中</option>
                                    <option>已解冻</option>
                                </select>
                                <input type="text" class="search-input" placeholder="搜索冻结原因...">
                                <button class="btn btn-primary">搜索</button>
                            </div>
                            <div class="action-buttons">
                                <button class="btn btn-success">新建冻结</button>
                            </div>
                        </div>

                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>冻结日期</th>
                                    <th>银行账户</th>
                                    <th>施工部</th>
                                    <th>项目名称</th>
                                    <th>冻结金额</th>
                                    <th>冻结原因</th>
                                    <th>冻结期限</th>
                                    <th>操作人</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>2024-01-12</td>
                                    <td>建行民工专户</td>
                                    <td>二部</td>
                                    <td>郦城一期C3</td>
                                    <td><span class="amount frozen">¥234,500</span></td>
                                    <td>质量保证金</td>
                                    <td>2024-03-12</td>
                                    <td>张经理</td>
                                    <td><span class="status-badge status-pending">冻结中</span></td>
                                    <td>
                                        <button class="btn btn-success" style="padding: 4px 8px; font-size: 12px;">解冻</button>
                                        <button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">详情</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2024-01-08</td>
                                    <td>农行民工专户</td>
                                    <td>三部</td>
                                    <td>西晶项目</td>
                                    <td><span class="amount frozen">¥300,000</span></td>
                                    <td>安全保证金</td>
                                    <td>2024-02-08</td>
                                    <td>李经理</td>
                                    <td><span class="status-badge status-pending">冻结中</span></td>
                                    <td>
                                        <button class="btn btn-success" style="padding: 4px 8px; font-size: 12px;">解冻</button>
                                        <button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">详情</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2023-12-20</td>
                                    <td>中行一般户</td>
                                    <td>四部</td>
                                    <td>城南项目</td>
                                    <td><span class="amount positive">¥150,000</span></td>
                                    <td>履约保证金</td>
                                    <td>2024-01-20</td>
                                    <td>王经理</td>
                                    <td><span class="status-badge status-approved">已解冻</span></td>
                                    <td><button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">详情</button></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- 银行日记页面 -->
                <div class="page" id="bank-journal">
                    <div class="page-content">
                        <div class="stats-cards">
                            <div class="stats-card">
                                <h3>本月流水笔数</h3>
                                <div class="value" style="color: #3498db;">156</div>
                            </div>
                            <div class="stats-card">
                                <h3>本月收入总额</h3>
                                <div class="value amount positive">¥8,450,000</div>
                            </div>
                            <div class="stats-card">
                                <h3>本月支出总额</h3>
                                <div class="value amount negative">¥6,230,000</div>
                            </div>
                            <div class="stats-card">
                                <h3>当前余额</h3>
                                <div class="value amount positive">¥2,458,900</div>
                            </div>
                        </div>

                        <div class="toolbar">
                            <div class="search-box">
                                <select class="form-control" style="width: 180px;" id="bank-filter">
                                    <option value="">全部银行账户</option>
                                    <option value="招行0801">招行0801</option>
                                    <option value="建行民工专户">建行民工专户</option>
                                    <option value="工行一般户">工行一般户</option>
                                    <option value="农行民工专户">农行民工专户</option>
                                    <option value="中行一般户">中行一般户</option>
                                </select>
                                <input type="date" class="form-control" style="width: 150px;" value="2024-01-01" id="date-from">
                                <span style="margin: 0 10px;">至</span>
                                <input type="date" class="form-control" style="width: 150px;" value="2024-01-31" id="date-to">
                                <input type="text" class="search-input" placeholder="搜索交易摘要..." id="search-text">
                                <button class="btn btn-primary" onclick="filterBankJournal()">搜索</button>
                            </div>
                            <div class="action-buttons">
                                <button class="btn btn-success">手工录入</button>
                                <button class="btn btn-primary">Excel导入</button>
                                <button class="btn btn-primary">银行对账</button>
                            </div>
                        </div>

                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>交易日期</th>
                                    <th>交易时间</th>
                                    <th>交易摘要</th>
                                    <th>对方账户</th>
                                    <th>收入金额</th>
                                    <th>支出金额</th>
                                    <th>账户余额</th>
                                    <th>分配状态</th>
                                    <th>录入人</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>2024-01-15</td>
                                    <td>14:30:25</td>
                                    <td>郦城项目工程款</td>
                                    <td>成都建设集团</td>
                                    <td><span class="amount positive">¥2,000,000</span></td>
                                    <td>-</td>
                                    <td><span class="amount positive">¥2,458,900</span></td>
                                    <td><span class="status-badge status-pending">部分分配</span></td>
                                    <td>张会计</td>
                                    <td>
                                        <button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;" onclick="showJournalDetail('J001')">详情</button>
                                        <button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">分配</button>
                                        <button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">编辑</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2024-01-15</td>
                                    <td>10:15:42</td>
                                    <td>钢筋材料款支付</td>
                                    <td>四川钢铁公司</td>
                                    <td>-</td>
                                    <td><span class="amount negative">¥458,900</span></td>
                                    <td><span class="amount positive">¥458,900</span></td>
                                    <td><span class="status-badge status-approved">已分配</span></td>
                                    <td>李会计</td>
                                    <td><button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;" onclick="showJournalDetail('J002')">详情</button></td>
                                </tr>
                                <tr>
                                    <td>2024-01-14</td>
                                    <td>16:45:18</td>
                                    <td>预付款收入</td>
                                    <td>眉山房地产公司</td>
                                    <td><span class="amount positive">¥1,200,000</span></td>
                                    <td>-</td>
                                    <td><span class="amount positive">¥917,800</span></td>
                                    <td><span class="status-badge status-rejected">待分配</span></td>
                                    <td>王会计</td>
                                    <td>
                                        <button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;" onclick="showJournalDetail('J003')">详情</button>
                                        <button class="btn btn-success" style="padding: 4px 8px; font-size: 12px;">分配</button>
                                        <button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">编辑</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2024-01-14</td>
                                    <td>09:20:35</td>
                                    <td>人工费支付</td>
                                    <td>建筑劳务公司</td>
                                    <td>-</td>
                                    <td><span class="amount negative">¥680,000</span></td>
                                    <td><span class="amount negative">¥282,200</span></td>
                                    <td><span class="status-badge status-approved">已分配</span></td>
                                    <td>赵会计</td>
                                    <td><button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;" onclick="showJournalDetail('J004')">详情</button></td>
                                </tr>
                                <tr>
                                    <td>2024-01-13</td>
                                    <td>15:30:12</td>
                                    <td>设备租赁费</td>
                                    <td>机械设备租赁公司</td>
                                    <td>-</td>
                                    <td><span class="amount negative">¥320,000</span></td>
                                    <td><span class="amount positive">¥962,200</span></td>
                                    <td><span class="status-badge status-approved">已分配</span></td>
                                    <td>孙会计</td>
                                    <td><button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;" onclick="showJournalDetail('J005')">详情</button></td>
                                </tr>
                                <tr>
                                    <td>2024-01-12</td>
                                    <td>11:45:28</td>
                                    <td>质量保证金冻结</td>
                                    <td>内部冻结</td>
                                    <td>-</td>
                                    <td><span class="amount frozen">¥234,500</span></td>
                                    <td><span class="amount positive">¥1,282,200</span></td>
                                    <td><span class="status-badge status-approved">已分配</span></td>
                                    <td>张经理</td>
                                    <td><button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;" onclick="showJournalDetail('J006')">详情</button></td>
                                </tr>
                            </tbody>
                        </table>

                        <!-- 银行流水导入表单（隐藏） -->
                        <div id="import-form" style="display: none; margin-top: 20px; padding: 20px; border: 1px solid #ddd; border-radius: 6px;">
                            <h4>Excel批量导入</h4>
                            <div class="form-row">
                                <div class="form-col">
                                    <label class="form-label">选择Excel文件</label>
                                    <input type="file" class="form-control" accept=".xlsx,.xls">
                                </div>
                                <div class="form-col">
                                    <label class="form-label">导入模式</label>
                                    <select class="form-control">
                                        <option>追加模式</option>
                                        <option>覆盖模式</option>
                                    </select>
                                </div>
                            </div>
                            <div style="margin-top: 15px;">
                                <button class="btn btn-success">开始导入</button>
                                <button class="btn btn-primary">下载模板</button>
                                <button class="btn" onclick="document.getElementById('import-form').style.display='none'">取消</button>
                            </div>
                        </div>

                        <!-- 手工录入表单（隐藏） -->
                        <div id="manual-form" style="display: none; margin-top: 20px; padding: 20px; border: 1px solid #ddd; border-radius: 6px;">
                            <h4>手工录入银行流水</h4>
                            <div class="form-row">
                                <div class="form-col">
                                    <label class="form-label">交易日期</label>
                                    <input type="date" class="form-control" value="2024-01-15">
                                </div>
                                <div class="form-col">
                                    <label class="form-label">交易时间</label>
                                    <input type="time" class="form-control" value="14:30">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">交易摘要</label>
                                <input type="text" class="form-control" placeholder="请输入交易摘要">
                            </div>
                            <div class="form-group">
                                <label class="form-label">对方账户</label>
                                <input type="text" class="form-control" placeholder="请输入对方账户信息">
                            </div>
                            <div class="form-row">
                                <div class="form-col">
                                    <label class="form-label">收入金额</label>
                                    <input type="number" class="form-control" placeholder="0.00" step="0.01">
                                </div>
                                <div class="form-col">
                                    <label class="form-label">支出金额</label>
                                    <input type="number" class="form-control" placeholder="0.00" step="0.01">
                                </div>
                            </div>
                            <div style="margin-top: 15px;">
                                <button class="btn btn-success">保存</button>
                                <button class="btn btn-primary">保存并继续</button>
                                <button class="btn" onclick="document.getElementById('manual-form').style.display='none'">取消</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 银行日记详情弹窗 -->
    <div id="journal-detail-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 8px; width: 800px; max-height: 80vh; overflow-y: auto;">
            <div style="padding: 20px; border-bottom: 1px solid #eee;">
                <h3 style="margin: 0; display: flex; justify-content: space-between; align-items: center;">
                    银行流水详情
                    <button onclick="closeJournalDetail()" style="background: none; border: none; font-size: 24px; cursor: pointer;">&times;</button>
                </h3>
            </div>
            <div style="padding: 20px;">
                <div id="journal-detail-content">
                    <!-- 详情内容将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面标题映射
        const pageTitles = {
            'bank-balance': '银行账户余额',
            'dept-balance': '施工部余额',
            'project-balance': '施工项目余额',
            'regional-balance': '异地区域项目余额',
            'account-allocation': '账户分配',
            'planned-payment': '计划支付',
            'dept-lending': '施工部拆借',
            'account-freeze': '账户冻结',
            'bank-journal': '招行0801（银行日记）'
        };

        // 切换页面
        function showPage(pageId) {
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });
            
            // 显示目标页面
            document.getElementById(pageId).classList.add('active');
            
            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 更新页面标题
            document.getElementById('page-title').textContent = pageTitles[pageId];
        }

        // 模拟银行账户数据
        const bankAccountData = [
            {
                bank: '招商银行',
                account: '****************',
                type: '一般户',
                balance: 2458900,
                planned: 458900,
                available: 2000000,
                updateTime: '2024-01-15 14:30:25'
            },
            {
                bank: '建设银行',
                account: '****************',
                type: '民工专户',
                balance: 1756200,
                planned: 256200,
                available: 1500000,
                updateTime: '2024-01-15 14:28:15'
            },
            {
                bank: '工商银行',
                account: '****************',
                type: '一般户',
                balance: 3200000,
                planned: 800000,
                available: 2400000,
                updateTime: '2024-01-15 14:25:42'
            }
        ];

        // 格式化金额
        function formatAmount(amount, type = 'default') {
            const formatted = '¥' + amount.toLocaleString();
            let className = 'amount';
            
            if (type === 'frozen' || type === 'planned') {
                className += ' frozen';
            } else if (amount > 0) {
                className += ' positive';
            } else if (amount < 0) {
                className += ' negative';
            }
            
            return `<span class="${className}">${formatted}</span>`;
        }

        // 渲染银行账户表格
        function renderBankBalanceTable() {
            const tbody = document.getElementById('bank-balance-table');
            let html = '';
            
            bankAccountData.forEach(item => {
                html += `
                    <tr>
                        <td>${item.bank}</td>
                        <td>${item.account}</td>
                        <td><span class="status-badge ${item.type === '民工专户' ? 'status-pending' : 'status-approved'}">${item.type}</span></td>
                        <td>${formatAmount(item.balance)}</td>
                        <td>${formatAmount(item.planned, 'planned')}</td>
                        <td>${formatAmount(item.available)}</td>
                        <td>${item.updateTime}</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">查看</button>
                            <button class="btn btn-success" style="padding: 4px 8px; font-size: 12px;">编辑</button>
                        </td>
                    </tr>
                `;
            });
            
            tbody.innerHTML = html;
        }

        // 显示导入表单
        function showImportForm() {
            document.getElementById('import-form').style.display = 'block';
            document.getElementById('manual-form').style.display = 'none';
        }

        // 显示手工录入表单
        function showManualForm() {
            document.getElementById('manual-form').style.display = 'block';
            document.getElementById('import-form').style.display = 'none';
        }

        // 银行日记筛选功能
        function filterBankJournal() {
            const bankFilter = document.getElementById('bank-filter').value;
            const dateFrom = document.getElementById('date-from').value;
            const dateTo = document.getElementById('date-to').value;
            const searchText = document.getElementById('search-text').value;

            console.log('筛选条件:', { bankFilter, dateFrom, dateTo, searchText });
            alert(`筛选条件：\n银行：${bankFilter || '全部'}\n日期：${dateFrom} 至 ${dateTo}\n关键词：${searchText || '无'}`);
        }

        // 显示银行日记详情
        function showJournalDetail(journalId) {
            const detailData = {
                'J001': {
                    id: 'J001',
                    date: '2024-01-15',
                    time: '14:30:25',
                    bank: '招商银行0801',
                    summary: '郦城项目工程款',
                    counterpart: '成都建设集团',
                    income: 2000000,
                    expense: 0,
                    balance: 2458900,
                    operator: '张会计',
                    status: '部分分配',
                    allocations: [
                        { dept: '二部', project: '郦城一期C3', amount: 1500000, reason: '工程进度款' }
                    ],
                    attachments: ['工程进度确认单.pdf', '发票.pdf'],
                    approvalHistory: [
                        { time: '2024-01-15 14:35', operator: '张会计', action: '录入流水', status: '已录入' },
                        { time: '2024-01-15 15:20', operator: '李经理', action: '部分分配', status: '待完成分配' }
                    ]
                },
                'J002': {
                    id: 'J002',
                    date: '2024-01-15',
                    time: '10:15:42',
                    bank: '建设银行民工专户',
                    summary: '钢筋材料款支付',
                    counterpart: '四川钢铁公司',
                    income: 0,
                    expense: 458900,
                    balance: 458900,
                    operator: '李会计',
                    status: '已分配',
                    allocations: [
                        { dept: '二部', project: '郦城一期C3', amount: 458900, reason: '钢筋材料采购' }
                    ],
                    attachments: ['采购合同.pdf', '材料验收单.pdf'],
                    approvalHistory: [
                        { time: '2024-01-15 09:30', operator: '李会计', action: '制单申请', status: '已制单' },
                        { time: '2024-01-15 09:45', operator: '张经理', action: '审批通过', status: '已审批' },
                        { time: '2024-01-15 10:15', operator: '系统', action: '自动分配', status: '已分配' }
                    ]
                }
            };

            const data = detailData[journalId] || detailData['J001'];

            const content = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                    <div>
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">基本信息</h4>
                        <table style="width: 100%; font-size: 14px;">
                            <tr><td style="padding: 5px 0; color: #666;">流水编号：</td><td style="padding: 5px 0;">${data.id}</td></tr>
                            <tr><td style="padding: 5px 0; color: #666;">交易日期：</td><td style="padding: 5px 0;">${data.date} ${data.time}</td></tr>
                            <tr><td style="padding: 5px 0; color: #666;">银行账户：</td><td style="padding: 5px 0;">${data.bank}</td></tr>
                            <tr><td style="padding: 5px 0; color: #666;">交易摘要：</td><td style="padding: 5px 0;">${data.summary}</td></tr>
                            <tr><td style="padding: 5px 0; color: #666;">对方账户：</td><td style="padding: 5px 0;">${data.counterpart}</td></tr>
                            <tr><td style="padding: 5px 0; color: #666;">录入人员：</td><td style="padding: 5px 0;">${data.operator}</td></tr>
                        </table>
                    </div>
                    <div>
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">金额信息</h4>
                        <table style="width: 100%; font-size: 14px;">
                            <tr><td style="padding: 5px 0; color: #666;">收入金额：</td><td style="padding: 5px 0; color: #27ae60; font-weight: 500;">¥${data.income.toLocaleString()}</td></tr>
                            <tr><td style="padding: 5px 0; color: #666;">支出金额：</td><td style="padding: 5px 0; color: #e74c3c; font-weight: 500;">¥${data.expense.toLocaleString()}</td></tr>
                            <tr><td style="padding: 5px 0; color: #666;">账户余额：</td><td style="padding: 5px 0; color: #2c3e50; font-weight: 500;">¥${data.balance.toLocaleString()}</td></tr>
                            <tr><td style="padding: 5px 0; color: #666;">分配状态：</td><td style="padding: 5px 0;"><span class="status-badge ${data.status === '已分配' ? 'status-approved' : 'status-pending'}">${data.status}</span></td></tr>
                        </table>
                    </div>
                </div>

                <div style="margin-bottom: 20px;">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">分配明细</h4>
                    <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="padding: 10px; text-align: left; border-bottom: 1px solid #dee2e6;">施工部</th>
                                <th style="padding: 10px; text-align: left; border-bottom: 1px solid #dee2e6;">项目名称</th>
                                <th style="padding: 10px; text-align: right; border-bottom: 1px solid #dee2e6;">分配金额</th>
                                <th style="padding: 10px; text-align: left; border-bottom: 1px solid #dee2e6;">分配原因</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${data.allocations.map(item => `
                                <tr>
                                    <td style="padding: 10px; border-bottom: 1px solid #dee2e6;">${item.dept}</td>
                                    <td style="padding: 10px; border-bottom: 1px solid #dee2e6;">${item.project}</td>
                                    <td style="padding: 10px; text-align: right; border-bottom: 1px solid #dee2e6; color: #27ae60; font-weight: 500;">¥${item.amount.toLocaleString()}</td>
                                    <td style="padding: 10px; border-bottom: 1px solid #dee2e6;">${item.reason}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>

                <div style="margin-bottom: 20px;">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">相关附件</h4>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                        ${data.attachments.map(file => `
                            <span style="padding: 5px 10px; background: #e3f2fd; color: #1976d2; border-radius: 4px; font-size: 12px; cursor: pointer;">${file}</span>
                        `).join('')}
                    </div>
                </div>

                <div>
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">操作历史</h4>
                    <div style="border-left: 3px solid #3498db; padding-left: 15px;">
                        ${data.approvalHistory.map(item => `
                            <div style="margin-bottom: 15px; padding-bottom: 15px; border-bottom: 1px solid #eee;">
                                <div style="font-weight: 500; color: #2c3e50;">${item.action}</div>
                                <div style="font-size: 12px; color: #666; margin-top: 5px;">
                                    ${item.time} | ${item.operator} | ${item.status}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;

            document.getElementById('journal-detail-content').innerHTML = content;
            document.getElementById('journal-detail-modal').style.display = 'block';
        }

        // 关闭详情弹窗
        function closeJournalDetail() {
            document.getElementById('journal-detail-modal').style.display = 'none';
        }

        // 添加按钮事件监听
        function addEventListeners() {
            // Excel导入按钮
            const importBtn = document.querySelector('.action-buttons .btn-primary:last-child');
            if (importBtn && importBtn.textContent === 'Excel导入') {
                importBtn.onclick = showImportForm;
            }

            // 手工录入按钮
            const manualBtn = document.querySelector('.action-buttons .btn-success');
            if (manualBtn && manualBtn.textContent === '手工录入') {
                manualBtn.onclick = showManualForm;
            }
        }

        // 模拟数据刷新
        function refreshData() {
            // 模拟加载效果
            const refreshBtn = document.querySelector('.header-actions .btn-success');
            if (refreshBtn) {
                refreshBtn.textContent = '刷新中...';
                refreshBtn.disabled = true;

                setTimeout(() => {
                    refreshBtn.textContent = '刷新数据';
                    refreshBtn.disabled = false;
                    alert('数据刷新完成！');
                }, 2000);
            }
        }

        // 模拟导出功能
        function exportData() {
            alert('正在导出Excel文件...');
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            renderBankBalanceTable();

            // 添加头部按钮事件
            const exportBtn = document.querySelector('.header-actions .btn-primary');
            if (exportBtn) exportBtn.onclick = exportData;

            const refreshBtn = document.querySelector('.header-actions .btn-success');
            if (refreshBtn) refreshBtn.onclick = refreshData;

            // 页面切换时重新绑定事件
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                        const target = mutation.target;
                        if (target.classList.contains('active') && target.id === 'bank-journal') {
                            addEventListeners();
                        }
                    }
                });
            });

            // 监听所有页面的class变化
            document.querySelectorAll('.page').forEach(page => {
                observer.observe(page, { attributes: true });
            });
        });
    </script>
</body>
</html>
