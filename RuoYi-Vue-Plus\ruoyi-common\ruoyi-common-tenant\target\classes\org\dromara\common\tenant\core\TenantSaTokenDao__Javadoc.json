{"doc": "\n SaToken 认证数据持久层 适配多租户\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "update", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 修修改指定key-value键值对 (过期时间不变)\r\n"}, {"name": "delete", "paramTypes": ["java.lang.String"], "doc": "\n 删除Value\r\n"}, {"name": "getTimeout", "paramTypes": ["java.lang.String"], "doc": "\n 获取Value的剩余存活时间 (单位: 秒)\r\n"}, {"name": "updateTimeout", "paramTypes": ["java.lang.String", "long"], "doc": "\n 修改Value的剩余存活时间 (单位: 秒)\r\n"}, {"name": "getObject", "paramTypes": ["java.lang.String"], "doc": "\n 获取Object，如无返空\r\n"}, {"name": "getObject", "paramTypes": ["java.lang.String", "java.lang.Class"], "doc": "\n 获取 Object (指定反序列化类型)，如无返空\r\n\r\n @param key 键名称\r\n @return object\r\n"}, {"name": "setObject", "paramTypes": ["java.lang.String", "java.lang.Object", "long"], "doc": "\n 写入Object，并设定存活时间 (单位: 秒)\r\n"}, {"name": "updateObject", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 更新Object (过期时间不变)\r\n"}, {"name": "deleteObject", "paramTypes": ["java.lang.String"], "doc": "\n 删除Object\r\n"}, {"name": "getObjectTimeout", "paramTypes": ["java.lang.String"], "doc": "\n 获取Object的剩余存活时间 (单位: 秒)\r\n"}, {"name": "updateObjectTimeout", "paramTypes": ["java.lang.String", "long"], "doc": "\n 修改Object的剩余存活时间 (单位: 秒)\r\n"}, {"name": "searchData", "paramTypes": ["java.lang.String", "java.lang.String", "int", "int", "boolean"], "doc": "\n 搜索数据\r\n"}], "constructors": []}