{"doc": "\n 租户助手\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "isEnable", "paramTypes": [], "doc": "\n 租户功能是否启用\r\n"}, {"name": "enableIgnore", "paramTypes": [], "doc": "\n 开启忽略租户(开启后需手动调用 {@link #disableIgnore()} 关闭)\r\n"}, {"name": "disableIgnore", "paramTypes": [], "doc": "\n 关闭忽略租户\r\n"}, {"name": "ignore", "paramTypes": ["java.lang.Runnable"], "doc": "\n 在忽略租户中执行\r\n\r\n @param handle 处理执行方法\r\n"}, {"name": "ignore", "paramTypes": ["java.util.function.Supplier"], "doc": "\n 在忽略租户中执行\r\n\r\n @param handle 处理执行方法\r\n"}, {"name": "setDynamic", "paramTypes": ["java.lang.String", "boolean"], "doc": "\n 设置动态租户(一直有效 需要手动清理)\r\n <p>\r\n 如果为未登录状态下 那么只在当前线程内生效\r\n\r\n @param tenantId 租户id\r\n @param global   是否全局生效\r\n"}, {"name": "getDynamic", "paramTypes": [], "doc": "\n 获取动态租户(一直有效 需要手动清理)\r\n <p>\r\n 如果为未登录状态下 那么只在当前线程内生效\r\n"}, {"name": "clearDynamic", "paramTypes": [], "doc": "\n 清除动态租户\r\n"}, {"name": "dynamic", "paramTypes": ["java.lang.String", "java.lang.Runnable"], "doc": "\n 在动态租户中执行\r\n\r\n @param handle 处理执行方法\r\n"}, {"name": "dynamic", "paramTypes": ["java.lang.String", "java.util.function.Supplier"], "doc": "\n 在动态租户中执行\r\n\r\n @param handle 处理执行方法\r\n"}, {"name": "getTenantId", "paramTypes": [], "doc": "\n 获取当前租户id(动态租户优先)\r\n"}], "constructors": []}