{"doc": "\n 系统访问日志情况信息 服务层处理\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "recordLogininfor", "paramTypes": ["org.dromara.common.log.event.LogininforEvent"], "doc": "\n 记录登录信息\r\n\r\n @param logininforEvent 登录事件\r\n"}, {"name": "insertLogininfor", "paramTypes": ["org.dromara.system.domain.bo.SysLogininforBo"], "doc": "\n 新增系统登录日志\r\n\r\n @param bo 访问日志对象\r\n"}, {"name": "selectLogininforList", "paramTypes": ["org.dromara.system.domain.bo.SysLogininforBo"], "doc": "\n 查询系统登录日志集合\r\n\r\n @param logininfor 访问日志对象\r\n @return 登录记录集合\r\n"}, {"name": "deleteLogininforByIds", "paramTypes": ["java.lang.Long[]"], "doc": "\n 批量删除系统登录日志\r\n\r\n @param infoIds 需要删除的登录日志ID\r\n @return 结果\r\n"}, {"name": "cleanLogininfor", "paramTypes": [], "doc": "\n 清空系统登录日志\r\n"}], "constructors": []}