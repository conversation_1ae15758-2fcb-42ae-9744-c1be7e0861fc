{"doc": "\n 租户管理\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysTenantBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询租户列表\r\n"}, {"name": "export", "paramTypes": ["org.dromara.system.domain.bo.SysTenantBo", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 导出租户列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 获取租户详细信息\r\n\r\n @param id 主键\r\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.SysTenantBo"], "doc": "\n 新增租户\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.SysTenantBo"], "doc": "\n 修改租户\r\n"}, {"name": "changeStatus", "paramTypes": ["org.dromara.system.domain.bo.SysTenantBo"], "doc": "\n 状态修改\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除租户\r\n\r\n @param ids 主键串\r\n"}, {"name": "dynamicTenant", "paramTypes": ["java.lang.String"], "doc": "\n 动态切换租户\r\n\r\n @param tenantId 租户ID\r\n"}, {"name": "dynamicClear", "paramTypes": [], "doc": "\n 清除动态租户\r\n"}, {"name": "syncTenantPackage", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 同步租户套餐\r\n\r\n @param tenantId  租户id\r\n @param packageId 套餐id\r\n"}, {"name": "syncTenantDict", "paramTypes": [], "doc": "\n 同步租户字典\r\n"}], "constructors": []}