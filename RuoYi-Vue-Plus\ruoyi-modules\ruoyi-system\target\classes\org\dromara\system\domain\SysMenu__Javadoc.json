{"doc": "\n 菜单权限表 sys_menu\r\n\r\n <AUTHOR> Li\r\n", "fields": [{"name": "menuId", "doc": "\n 菜单ID\r\n"}, {"name": "parentId", "doc": "\n 父菜单ID\r\n"}, {"name": "menuName", "doc": "\n 菜单名称\r\n"}, {"name": "orderNum", "doc": "\n 显示顺序\r\n"}, {"name": "path", "doc": "\n 路由地址\r\n"}, {"name": "component", "doc": "\n 组件路径\r\n"}, {"name": "queryParam", "doc": "\n 路由参数\r\n"}, {"name": "isFrame", "doc": "\n 是否为外链（0是 1否）\r\n"}, {"name": "isCache", "doc": "\n 是否缓存（0缓存 1不缓存）\r\n"}, {"name": "menuType", "doc": "\n 类型（M目录 C菜单 F按钮）\r\n"}, {"name": "visible", "doc": "\n 显示状态（0显示 1隐藏）\r\n"}, {"name": "status", "doc": "\n 菜单状态（0正常 1停用）\r\n"}, {"name": "perms", "doc": "\n 权限字符串\r\n"}, {"name": "icon", "doc": "\n 菜单图标\r\n"}, {"name": "remark", "doc": "\n 备注\r\n"}, {"name": "parentName", "doc": "\n 父菜单名称\r\n"}, {"name": "children", "doc": "\n 子菜单\r\n"}], "enumConstants": [], "methods": [{"name": "getRouteName", "paramTypes": [], "doc": "\n 获取路由名称\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": [], "doc": "\n 获取路由地址\r\n"}, {"name": "getComponentInfo", "paramTypes": [], "doc": "\n 获取组件信息\r\n"}, {"name": "isMenuFrame", "paramTypes": [], "doc": "\n 是否为菜单内部跳转\r\n"}, {"name": "isInnerLink", "paramTypes": [], "doc": "\n 是否为内链组件\r\n"}, {"name": "isParentView", "paramTypes": [], "doc": "\n 是否为parent_view组件\r\n"}, {"name": "innerLinkReplaceEach", "paramTypes": ["java.lang.String"], "doc": "\n 内链域名特殊字符替换\r\n"}], "constructors": []}