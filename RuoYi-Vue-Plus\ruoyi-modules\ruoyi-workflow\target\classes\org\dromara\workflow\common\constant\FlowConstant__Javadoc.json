{"doc": "\n 工作流常量\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "INITIATOR", "doc": "\n 流程发起人\r\n"}, {"name": "BUSINESS_ID", "doc": "\n 业务id\r\n"}, {"name": "DELEGATE_TASK", "doc": "\n 委托\r\n"}, {"name": "TRANSFER_TASK", "doc": "\n 转办\r\n"}, {"name": "ADD_SIGNATURE", "doc": "\n 加签\r\n"}, {"name": "REDUCTION_SIGNATURE", "doc": "\n 减签\r\n"}, {"name": "CATEGORY_ID_TO_NAME", "doc": "\n 流程分类Id转名称\r\n"}, {"name": "FLOW_CATEGORY_NAME", "doc": "\n 流程分类名称\r\n"}, {"name": "FLOW_CATEGORY_ID", "doc": "\n 默认租户OA申请分类id\r\n"}, {"name": "SUBMIT", "doc": "\n 是否为申请人提交常量\r\n"}, {"name": "FLOW_COPY_LIST", "doc": "\n 抄送常量\r\n"}, {"name": "MESSAGE_TYPE", "doc": "\n 消息类型常量\r\n"}, {"name": "MESSAGE_NOTICE", "doc": "\n 消息通知常量\r\n"}, {"name": "WF_TASK_STATUS", "doc": "\n 任务状态\r\n"}], "enumConstants": [], "methods": [], "constructors": []}