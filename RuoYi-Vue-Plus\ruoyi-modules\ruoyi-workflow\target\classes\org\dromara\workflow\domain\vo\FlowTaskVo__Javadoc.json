{"doc": "\n 任务视图\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "createTime", "doc": "\n 创建时间\r\n"}, {"name": "updateTime", "doc": "\n 更新时间\r\n"}, {"name": "tenantId", "doc": "\n 租户ID\r\n"}, {"name": "delFlag", "doc": "\n 删除标记\r\n"}, {"name": "definitionId", "doc": "\n 对应flow_definition表的id\r\n"}, {"name": "instanceId", "doc": "\n 流程实例表id\r\n"}, {"name": "flowName", "doc": "\n 流程定义名称\r\n"}, {"name": "businessId", "doc": "\n 业务id\r\n"}, {"name": "nodeCode", "doc": "\n 节点编码\r\n"}, {"name": "nodeName", "doc": "\n 节点名称\r\n"}, {"name": "nodeType", "doc": "\n 节点类型（0开始节点 1中间节点 2结束节点 3互斥网关 4并行网关）\r\n"}, {"name": "permissionList", "doc": "\n 权限标识 permissionFlag的list形式\r\n"}, {"name": "userList", "doc": "\n 流程用户列表\r\n"}, {"name": "formCustom", "doc": "\n 审批表单是否自定义（Y是 N否）\r\n"}, {"name": "formPath", "doc": "\n 审批表单\r\n"}, {"name": "flowCode", "doc": "\n 流程定义编码\r\n"}, {"name": "version", "doc": "\n 流程版本号\r\n"}, {"name": "flowStatus", "doc": "\n 流程状态\r\n"}, {"name": "category", "doc": "\n 流程分类id\r\n"}, {"name": "categoryName", "doc": "\n 流程分类名称\r\n"}, {"name": "flowStatusName", "doc": "\n 流程状态\r\n"}, {"name": "type", "doc": "\n 办理人类型\r\n"}, {"name": "assigneeIds", "doc": "\n 办理人ids\r\n"}, {"name": "assignee<PERSON><PERSON>s", "doc": "\n 办理人名称\r\n"}, {"name": "processedBy", "doc": "\n 抄送人id\r\n"}, {"name": "processedByName", "doc": "\n 抄送人名称\r\n"}, {"name": "nodeRatio", "doc": "\n 流程签署比例值 大于0为票签，会签\r\n"}, {"name": "createBy", "doc": "\n 申请人id\r\n"}, {"name": "createByName", "doc": "\n 申请人名称\r\n"}, {"name": "applyNode", "doc": "\n 是否为申请人节点\r\n"}, {"name": "buttonList", "doc": "\n 按钮权限\r\n"}], "enumConstants": [], "methods": [], "constructors": []}