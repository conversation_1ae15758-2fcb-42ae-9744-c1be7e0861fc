{"doc": "\n 测试单表Service接口\r\n\r\n <AUTHOR>\r\n @date 2021-07-26\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询单个\r\n\r\n @return\r\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.demo.domain.bo.TestDemoBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询列表\r\n"}, {"name": "customPageList", "paramTypes": ["org.dromara.demo.domain.bo.TestDemoBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 自定义分页查询\r\n"}, {"name": "queryList", "paramTypes": ["org.dromara.demo.domain.bo.TestDemoBo"], "doc": "\n 查询列表\r\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.demo.domain.bo.TestDemoBo"], "doc": "\n 根据新增业务对象插入测试单表\r\n\r\n @param bo 测试单表新增业务对象\r\n @return\r\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.demo.domain.bo.TestDemoBo"], "doc": "\n 根据编辑业务对象修改测试单表\r\n\r\n @param bo 测试单表编辑业务对象\r\n @return\r\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": "\n 校验并删除数据\r\n\r\n @param ids     主键集合\r\n @param isValid 是否校验,true-删除前校验,false-不校验\r\n @return\r\n"}, {"name": "saveBatch", "paramTypes": ["java.util.List"], "doc": "\n 批量保存\r\n"}], "constructors": []}