{"doc": "\n 租户套餐Service业务层处理\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询租户套餐\r\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.system.domain.bo.SysTenantPackageBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询租户套餐列表\r\n"}, {"name": "queryList", "paramTypes": ["org.dromara.system.domain.bo.SysTenantPackageBo"], "doc": "\n 查询租户套餐列表\r\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.system.domain.bo.SysTenantPackageBo"], "doc": "\n 新增租户套餐\r\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.system.domain.bo.SysTenantPackageBo"], "doc": "\n 修改租户套餐\r\n"}, {"name": "checkPackageNameUnique", "paramTypes": ["org.dromara.system.domain.bo.SysTenantPackageBo"], "doc": "\n 校验套餐名称是否唯一\r\n"}, {"name": "updatePackageStatus", "paramTypes": ["org.dromara.system.domain.bo.SysTenantPackageBo"], "doc": "\n 修改套餐状态\r\n\r\n @param bo 套餐信息\r\n @return 结果\r\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": "\n 批量删除租户套餐\r\n"}], "constructors": []}