{"doc": "\n SmsDao缓存配置 (使用框架自带RedisUtils实现 协议统一)\r\n <p>主要用于短信重试和拦截的缓存\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "set", "paramTypes": ["java.lang.String", "java.lang.Object", "long"], "doc": "\n 存储\r\n\r\n @param key       键\r\n @param value     值\r\n @param cacheTime 缓存时间（单位：秒)\r\n"}, {"name": "set", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 存储\r\n\r\n @param key   键\r\n @param value 值\r\n"}, {"name": "get", "paramTypes": ["java.lang.String"], "doc": "\n 读取\r\n\r\n @param key 键\r\n @return 值\r\n"}, {"name": "remove", "paramTypes": ["java.lang.String"], "doc": "\n remove\r\n <p> 根据key移除缓存\r\n\r\n @param key 缓存键\r\n @return 被删除的value\r\n <AUTHOR>\r\n"}, {"name": "clean", "paramTypes": [], "doc": "\n 清空\r\n"}], "constructors": []}