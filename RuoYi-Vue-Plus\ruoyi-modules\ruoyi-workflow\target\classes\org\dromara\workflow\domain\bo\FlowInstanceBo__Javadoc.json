{"doc": "\n 流程实例请求对象\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "flowName", "doc": "\n 流程定义名称\r\n"}, {"name": "flowCode", "doc": "\n 流程定义编码\r\n"}, {"name": "startUserId", "doc": "\n 任务发起人\r\n"}, {"name": "businessId", "doc": "\n 业务id\r\n"}, {"name": "category", "doc": "\n 流程分类id\r\n"}, {"name": "nodeName", "doc": "\n 任务名称\r\n"}, {"name": "createByIds", "doc": "\n 申请人Ids\r\n"}], "enumConstants": [], "methods": [], "constructors": []}