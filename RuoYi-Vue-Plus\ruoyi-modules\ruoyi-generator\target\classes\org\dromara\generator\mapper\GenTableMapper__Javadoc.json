{"doc": "\n 业务 数据层\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectGenTableAll", "paramTypes": [], "doc": "\n 查询所有表信息\r\n\r\n @return 表信息集合\r\n"}, {"name": "selectGenTableById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询表ID业务信息\r\n\r\n @param id 业务ID\r\n @return 业务信息\r\n"}, {"name": "selectGenTableByName", "paramTypes": ["java.lang.String"], "doc": "\n 查询表名称业务信息\r\n\r\n @param tableName 表名称\r\n @return 业务信息\r\n"}, {"name": "selectTableNameList", "paramTypes": ["java.lang.String"], "doc": "\n 查询指定数据源下的所有表名列表\r\n\r\n @param dataName 数据源名称，用于选择不同的数据源\r\n @return 当前数据库中的表名列表\r\n\r\n @DS(\"\") 使用默认数据源执行查询操作\r\n"}], "constructors": []}