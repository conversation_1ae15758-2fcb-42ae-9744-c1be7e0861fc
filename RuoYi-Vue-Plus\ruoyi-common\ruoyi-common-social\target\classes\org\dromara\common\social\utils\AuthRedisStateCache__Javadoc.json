{"doc": "\n 授权状态缓存\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "cache", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 存入缓存\r\n\r\n @param key   缓存key\r\n @param value 缓存内容\r\n"}, {"name": "cache", "paramTypes": ["java.lang.String", "java.lang.String", "long"], "doc": "\n 存入缓存\r\n\r\n @param key     缓存key\r\n @param value   缓存内容\r\n @param timeout 指定缓存过期时间(毫秒)\r\n"}, {"name": "get", "paramTypes": ["java.lang.String"], "doc": "\n 获取缓存内容\r\n\r\n @param key 缓存key\r\n @return 缓存内容\r\n"}, {"name": "<PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String"], "doc": "\n 是否存在key，如果对应key的value值已过期，也返回false\r\n\r\n @param key 缓存key\r\n @return true：存在key，并且value没过期；false：key不存在或者已过期\r\n"}], "constructors": []}