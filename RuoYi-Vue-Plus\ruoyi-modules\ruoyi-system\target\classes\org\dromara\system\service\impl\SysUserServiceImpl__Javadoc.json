{"doc": "\n 用户 业务层处理\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectUserExportList", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo"], "doc": "\n 根据条件分页查询用户列表\r\n\r\n @param user 用户信息\r\n @return 用户信息集合信息\r\n"}, {"name": "selectAllocatedList", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 根据条件分页查询已分配用户角色列表\r\n\r\n @param user 用户信息\r\n @return 用户信息集合信息\r\n"}, {"name": "selectUnallocatedList", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 根据条件分页查询未分配用户角色列表\r\n\r\n @param user 用户信息\r\n @return 用户信息集合信息\r\n"}, {"name": "selectUserByUserName", "paramTypes": ["java.lang.String"], "doc": "\n 通过用户名查询用户\r\n\r\n @param userName 用户名\r\n @return 用户对象信息\r\n"}, {"name": "selectUserByPhonenumber", "paramTypes": ["java.lang.String"], "doc": "\n 通过手机号查询用户\r\n\r\n @param phonenumber 手机号\r\n @return 用户对象信息\r\n"}, {"name": "selectUserById", "paramTypes": ["java.lang.Long"], "doc": "\n 通过用户ID查询用户\r\n\r\n @param userId 用户ID\r\n @return 用户对象信息\r\n"}, {"name": "selectUserByIds", "paramTypes": ["java.util.List", "java.lang.Long"], "doc": "\n 通过用户ID串查询用户\r\n\r\n @param userIds 用户ID串\r\n @param deptId  部门id\r\n @return 用户列表信息\r\n"}, {"name": "selectUserRoleGroup", "paramTypes": ["java.lang.Long"], "doc": "\n 查询用户所属角色组\r\n\r\n @param userId 用户ID\r\n @return 结果\r\n"}, {"name": "selectUserPostGroup", "paramTypes": ["java.lang.Long"], "doc": "\n 查询用户所属岗位组\r\n\r\n @param userId 用户ID\r\n @return 结果\r\n"}, {"name": "checkUserNameUnique", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo"], "doc": "\n 校验用户名称是否唯一\r\n\r\n @param user 用户信息\r\n @return 结果\r\n"}, {"name": "checkPhoneUnique", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo"], "doc": "\n 校验手机号码是否唯一\r\n\r\n @param user 用户信息\r\n"}, {"name": "checkEmailUnique", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo"], "doc": "\n 校验email是否唯一\r\n\r\n @param user 用户信息\r\n"}, {"name": "checkUserAllowed", "paramTypes": ["java.lang.Long"], "doc": "\n 校验用户是否允许操作\r\n\r\n @param userId 用户ID\r\n"}, {"name": "checkUserDataScope", "paramTypes": ["java.lang.Long"], "doc": "\n 校验用户是否有数据权限\r\n\r\n @param userId 用户id\r\n"}, {"name": "insertUser", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo"], "doc": "\n 新增保存用户信息\r\n\r\n @param user 用户信息\r\n @return 结果\r\n"}, {"name": "registerUser", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo", "java.lang.String"], "doc": "\n 注册用户信息\r\n\r\n @param user 用户信息\r\n @return 结果\r\n"}, {"name": "updateUser", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo"], "doc": "\n 修改保存用户信息\r\n\r\n @param user 用户信息\r\n @return 结果\r\n"}, {"name": "insertUserAuth", "paramTypes": ["java.lang.Long", "java.lang.Long[]"], "doc": "\n 用户授权角色\r\n\r\n @param userId  用户ID\r\n @param roleIds 角色组\r\n"}, {"name": "updateUserStatus", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 修改用户状态\r\n\r\n @param userId 用户ID\r\n @param status 帐号状态\r\n @return 结果\r\n"}, {"name": "updateUserProfile", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo"], "doc": "\n 修改用户基本信息\r\n\r\n @param user 用户信息\r\n @return 结果\r\n"}, {"name": "updateUserAvatar", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 修改用户头像\r\n\r\n @param userId 用户ID\r\n @param avatar 头像地址\r\n @return 结果\r\n"}, {"name": "resetUserPwd", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 重置用户密码\r\n\r\n @param userId   用户ID\r\n @param password 密码\r\n @return 结果\r\n"}, {"name": "insertUserRole", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo", "boolean"], "doc": "\n 新增用户角色信息\r\n\r\n @param user  用户对象\r\n @param clear 清除已存在的关联数据\r\n"}, {"name": "insertUserPost", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo", "boolean"], "doc": "\n 新增用户岗位信息\r\n\r\n @param user  用户对象\r\n @param clear 清除已存在的关联数据\r\n"}, {"name": "insertUserRole", "paramTypes": ["java.lang.Long", "java.lang.Long[]", "boolean"], "doc": "\n 新增用户角色信息\r\n\r\n @param userId  用户ID\r\n @param roleIds 角色组\r\n @param clear   清除已存在的关联数据\r\n"}, {"name": "deleteUserById", "paramTypes": ["java.lang.Long"], "doc": "\n 通过用户ID删除用户\r\n\r\n @param userId 用户ID\r\n @return 结果\r\n"}, {"name": "deleteUserByIds", "paramTypes": ["java.lang.Long[]"], "doc": "\n 批量删除用户信息\r\n\r\n @param userIds 需要删除的用户ID\r\n @return 结果\r\n"}, {"name": "selectUserListByDept", "paramTypes": ["java.lang.Long"], "doc": "\n 通过部门id查询当前部门所有用户\r\n\r\n @param deptId 部门ID\r\n @return 用户信息集合信息\r\n"}, {"name": "selectUserNameById", "paramTypes": ["java.lang.Long"], "doc": "\n 通过用户ID查询用户账户\r\n\r\n @param userId 用户ID\r\n @return 用户账户\r\n"}, {"name": "selectNicknameById", "paramTypes": ["java.lang.Long"], "doc": "\n 通过用户ID查询用户账户\r\n\r\n @param userId 用户ID\r\n @return 用户账户\r\n"}, {"name": "selectNicknameByIds", "paramTypes": ["java.lang.String"], "doc": "\n 通过用户ID查询用户账户\r\n\r\n @param userIds 用户ID 多个用逗号隔开\r\n @return 用户账户\r\n"}, {"name": "selectPhonenumberById", "paramTypes": ["java.lang.Long"], "doc": "\n 通过用户ID查询用户手机号\r\n\r\n @param userId 用户id\r\n @return 用户手机号\r\n"}, {"name": "selectEmailById", "paramTypes": ["java.lang.Long"], "doc": "\n 通过用户ID查询用户邮箱\r\n\r\n @param userId 用户id\r\n @return 用户邮箱\r\n"}, {"name": "selectListByIds", "paramTypes": ["java.util.List"], "doc": "\n 通过用户ID查询用户列表\r\n\r\n @param userIds 用户ids\r\n @return 用户列表\r\n"}, {"name": "selectUserIdsByRoleIds", "paramTypes": ["java.util.List"], "doc": "\n 通过角色ID查询用户ID\r\n\r\n @param roleIds 角色ids\r\n @return 用户ids\r\n"}, {"name": "selectUsersByRoleIds", "paramTypes": ["java.util.List"], "doc": "\n 通过角色ID查询用户\r\n\r\n @param roleIds 角色ids\r\n @return 用户\r\n"}, {"name": "selectUsersByDeptIds", "paramTypes": ["java.util.List"], "doc": "\n 通过部门ID查询用户\r\n\r\n @param deptIds 部门ids\r\n @return 用户\r\n"}, {"name": "selectUsersByPostIds", "paramTypes": ["java.util.List"], "doc": "\n 通过岗位ID查询用户\r\n\r\n @param postIds 岗位ids\r\n @return 用户\r\n"}, {"name": "selectUserNamesByIds", "paramTypes": ["java.util.List"], "doc": "\n 根据用户 ID 列表查询用户名称映射关系\r\n\r\n @param userIds 用户 ID 列表\r\n @return Map，其中 key 为用户 ID，value 为对应的用户名称\r\n"}, {"name": "selectRoleNamesByIds", "paramTypes": ["java.util.List"], "doc": "\n 根据角色 ID 列表查询角色名称映射关系\r\n\r\n @param roleIds 角色 ID 列表\r\n @return Map，其中 key 为角色 ID，value 为对应的角色名称\r\n"}, {"name": "selectDeptNamesByIds", "paramTypes": ["java.util.List"], "doc": "\n 根据部门 ID 列表查询部门名称映射关系\r\n\r\n @param deptIds 部门 ID 列表\r\n @return Map，其中 key 为部门 ID，value 为对应的部门名称\r\n"}, {"name": "selectPostNamesByIds", "paramTypes": ["java.util.List"], "doc": "\n 根据岗位 ID 列表查询岗位名称映射关系\r\n\r\n @param postIds 岗位 ID 列表\r\n @return Map，其中 key 为岗位 ID，value 为对应的岗位名称\r\n"}], "constructors": []}