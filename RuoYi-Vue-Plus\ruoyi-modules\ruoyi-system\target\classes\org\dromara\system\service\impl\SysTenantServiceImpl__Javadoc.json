{"doc": "\n 租户Service业务层处理\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询租户\r\n"}, {"name": "queryByTenantId", "paramTypes": ["java.lang.String"], "doc": "\n 基于租户ID查询租户\r\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.system.domain.bo.SysTenantBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询租户列表\r\n"}, {"name": "queryList", "paramTypes": ["org.dromara.system.domain.bo.SysTenantBo"], "doc": "\n 查询租户列表\r\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.system.domain.bo.SysTenantBo"], "doc": "\n 新增租户\r\n"}, {"name": "generateTenantId", "paramTypes": ["java.util.List"], "doc": "\n 生成租户id\r\n\r\n @param tenantIds 已有租户id列表\r\n @return 租户id\r\n"}, {"name": "createTenantRole", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 根据租户菜单创建租户角色\r\n\r\n @param tenantId  租户编号\r\n @param packageId 租户套餐id\r\n @return 角色id\r\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.system.domain.bo.SysTenantBo"], "doc": "\n 修改租户\r\n"}, {"name": "updateTenantStatus", "paramTypes": ["org.dromara.system.domain.bo.SysTenantBo"], "doc": "\n 修改租户状态\r\n\r\n @param bo 租户信息\r\n @return 结果\r\n"}, {"name": "checkTenantAllowed", "paramTypes": ["java.lang.String"], "doc": "\n 校验租户是否允许操作\r\n\r\n @param tenantId 租户ID\r\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": "\n 批量删除租户\r\n"}, {"name": "checkCompanyNameUnique", "paramTypes": ["org.dromara.system.domain.bo.SysTenantBo"], "doc": "\n 校验企业名称是否唯一\r\n"}, {"name": "checkAccountBalance", "paramTypes": ["java.lang.String"], "doc": "\n 校验账号余额\r\n"}, {"name": "checkExpireTime", "paramTypes": ["java.lang.String"], "doc": "\n 校验有效期\r\n"}, {"name": "syncTenantPackage", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 同步租户套餐\r\n"}, {"name": "syncTenantDict", "paramTypes": [], "doc": "\n 同步租户字典\r\n"}], "constructors": []}