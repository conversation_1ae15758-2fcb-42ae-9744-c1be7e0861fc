{"doc": "\n 角色信息业务对象 sys_role\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "roleId", "doc": "\n 角色ID\r\n"}, {"name": "<PERSON><PERSON><PERSON>", "doc": "\n 角色名称\r\n"}, {"name": "<PERSON><PERSON><PERSON>", "doc": "\n 角色权限字符串\r\n"}, {"name": "roleSort", "doc": "\n 显示顺序\r\n"}, {"name": "dataScope", "doc": "\n 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限 5：仅本人数据权限 6：部门及以下或本人数据权限）\r\n"}, {"name": "menu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doc": "\n 菜单树选择项是否关联显示\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doc": "\n 部门树选择项是否关联显示\r\n"}, {"name": "status", "doc": "\n 角色状态（0正常 1停用）\r\n"}, {"name": "remark", "doc": "\n 备注\r\n"}, {"name": "menuIds", "doc": "\n 菜单组\r\n"}, {"name": "deptIds", "doc": "\n 部门组（数据权限）\r\n"}], "enumConstants": [], "methods": [], "constructors": []}