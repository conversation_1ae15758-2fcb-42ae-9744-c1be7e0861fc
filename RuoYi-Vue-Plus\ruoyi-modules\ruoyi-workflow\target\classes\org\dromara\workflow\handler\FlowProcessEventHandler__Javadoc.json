{"doc": "\n 流程监听服务\r\n\r\n <AUTHOR>\r\n @date 2024-06-02\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "processHandler", "paramTypes": ["java.lang.String", "org.dromara.warm.flow.core.entity.Instance", "java.lang.String", "java.util.Map", "boolean"], "doc": "\n 总体流程监听(例如: 草稿，撤销，退回，作废，终止，已完成，单任务完成等)\r\n\r\n @param flowCode   流程定义编码\r\n @param instance   实例数据\r\n @param status     流程状态\r\n @param params     办理参数\r\n @param submit     当为true时为申请人节点办理\r\n"}, {"name": "processTaskHandler", "paramTypes": ["java.lang.String", "org.dromara.warm.flow.core.entity.Instance", "java.lang.Long"], "doc": "\n 执行创建任务监听\r\n\r\n @param flowCode   流程定义编码\r\n @param instance   实例数据\r\n @param taskId     任务id\r\n"}, {"name": "processDeleteHandler", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 删除流程监听\r\n\r\n @param flowCode    流程定义编码\r\n @param businessId  业务ID\r\n"}], "constructors": []}