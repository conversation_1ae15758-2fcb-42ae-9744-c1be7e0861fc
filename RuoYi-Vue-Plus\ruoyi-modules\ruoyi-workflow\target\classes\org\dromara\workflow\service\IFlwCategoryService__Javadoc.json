{"doc": "\n 流程分类Service接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询流程分类\r\n\r\n @param categoryId 主键\r\n @return 流程分类\r\n"}, {"name": "selectCategoryNameById", "paramTypes": ["java.lang.Long"], "doc": "\n 根据流程分类ID查询流程分类名称\r\n\r\n @param categoryId 流程分类ID\r\n @return 流程分类名称\r\n"}, {"name": "queryList", "paramTypes": ["org.dromara.workflow.domain.bo.FlowCategoryBo"], "doc": "\n 查询符合条件的流程分类列表\r\n\r\n @param bo 查询条件\r\n @return 流程分类列表\r\n"}, {"name": "selectCategoryTreeList", "paramTypes": ["org.dromara.workflow.domain.bo.FlowCategoryBo"], "doc": "\n 查询流程分类树结构信息\r\n\r\n @param category 流程分类信息\r\n @return 流程分类树信息集合\r\n"}, {"name": "checkCategoryDataScope", "paramTypes": ["java.lang.Long"], "doc": "\n 校验流程分类是否有数据权限\r\n\r\n @param categoryId 流程分类ID\r\n"}, {"name": "checkCategoryNameUnique", "paramTypes": ["org.dromara.workflow.domain.bo.FlowCategoryBo"], "doc": "\n 校验流程分类名称是否唯一\r\n\r\n @param category 流程分类信息\r\n @return 结果\r\n"}, {"name": "checkCategoryExistDefinition", "paramTypes": ["java.lang.Long"], "doc": "\n 查询流程分类是否存在流程定义\r\n\r\n @param categoryId 流程分类ID\r\n @return 结果 true 存在 false 不存在\r\n"}, {"name": "hasChildByCategoryId", "paramTypes": ["java.lang.Long"], "doc": "\n 是否存在流程分类子节点\r\n\r\n @param categoryId 流程分类ID\r\n @return 结果\r\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.workflow.domain.bo.FlowCategoryBo"], "doc": "\n 新增流程分类\r\n\r\n @param bo 流程分类\r\n @return 是否新增成功\r\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.workflow.domain.bo.FlowCategoryBo"], "doc": "\n 修改流程分类\r\n\r\n @param bo 流程分类\r\n @return 是否修改成功\r\n"}, {"name": "deleteWithValidById", "paramTypes": ["java.lang.Long"], "doc": "\n 删除流程分类信息\r\n\r\n @param categoryId 主键\r\n @return 是否删除成功\r\n"}], "constructors": []}