{"doc": "\n 通用 工作流服务\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "buildUser", "paramTypes": ["java.util.List"], "doc": "\n 构建工作流用户\r\n\r\n @param permissionList 办理用户\r\n @return 用户\r\n"}, {"name": "sendMessage", "paramTypes": ["java.lang.String", "java.lang.Long", "java.util.List", "java.lang.String"], "doc": "\n 发送消息\r\n\r\n @param flowName    流程定义名称\r\n @param instId      实例id\r\n @param messageType 消息类型\r\n @param message     消息内容，为空则发送默认配置的消息内容\r\n"}, {"name": "applyNodeCode", "paramTypes": ["java.lang.Long"], "doc": "\n 申请人节点编码\r\n\r\n @param definitionId 流程定义id\r\n @return 申请人节点编码\r\n"}], "constructors": []}