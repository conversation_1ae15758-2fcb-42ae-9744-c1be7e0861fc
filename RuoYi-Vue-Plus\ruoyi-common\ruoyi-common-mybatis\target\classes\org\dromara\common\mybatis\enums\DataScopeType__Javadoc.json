{"doc": "\n 数据权限类型枚举\r\n <p>\r\n 支持使用 SpEL 模板表达式定义 SQL 查询条件\r\n 内置数据：\r\n - {@code user}: 当前登录用户信息，参考 {@link LoginUser}\r\n 内置服务：\r\n - {@code sdss}: 系统数据权限服务，参考 ISysDataScopeService\r\n 如需扩展数据，可以通过 {@link DataPermissionHelper} 进行操作\r\n 如需扩展服务，可以通过 ISysDataScopeService 自行编写\r\n </p>\r\n\r\n <AUTHOR> Li\r\n @version 3.5.0\r\n", "fields": [{"name": "sqlTemplate", "doc": "\n SpEL 模板表达式，用于构建 SQL 查询条件\r\n"}, {"name": "elseSql", "doc": "\n 如果不满足 {@code sqlTemplate} 的条件，则使用此默认 SQL 表达式\r\n"}], "enumConstants": [{"name": "ALL", "doc": "\n 全部数据权限\r\n"}, {"name": "CUSTOM", "doc": "\n 自定数据权限\r\n"}, {"name": "DEPT", "doc": "\n 部门数据权限\r\n"}, {"name": "DEPT_AND_CHILD", "doc": "\n 部门及以下数据权限\r\n"}, {"name": "SELF", "doc": "\n 仅本人数据权限\r\n"}, {"name": "DEPT_AND_CHILD_OR_SELF", "doc": "\n 部门及以下或本人数据权限\r\n"}], "methods": [{"name": "findCode", "paramTypes": ["java.lang.String"], "doc": "\n 根据枚举代码查找对应的枚举值\r\n\r\n @param code 枚举代码\r\n @return 对应的枚举值，如果未找到则返回 null\r\n"}], "constructors": []}