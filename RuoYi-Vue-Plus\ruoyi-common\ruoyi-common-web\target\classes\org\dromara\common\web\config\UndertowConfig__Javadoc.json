{"doc": "\n Undertow 自定义配置\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "customize", "paramTypes": ["org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory"], "doc": "\n 自定义 Undertow 配置\r\n <p>\r\n 主要配置内容包括：\r\n 1. 配置 WebSocket 部署信息\r\n 2. 在虚拟线程模式下使用虚拟线程池\r\n 3. 禁用不安全的 HTTP 方法，如 CONNECT、TRACE、TRACK\r\n </p>\r\n\r\n @param factory Undertow 的 Web 服务器工厂\r\n"}], "constructors": []}