{"doc": "\n 社会化关系Service接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.String"], "doc": "\n 查询社会化关系\r\n"}, {"name": "queryList", "paramTypes": ["org.dromara.system.domain.bo.SysSocialBo"], "doc": "\n 查询社会化关系列表\r\n"}, {"name": "queryListByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 查询社会化关系列表\r\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.system.domain.bo.SysSocialBo"], "doc": "\n 新增授权关系\r\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.system.domain.bo.SysSocialBo"], "doc": "\n 更新社会化关系\r\n"}, {"name": "deleteWithValidById", "paramTypes": ["java.lang.Long"], "doc": "\n 删除社会化关系信息\r\n"}, {"name": "selectByAuthId", "paramTypes": ["java.lang.String"], "doc": "\n 根据 authId 查询\r\n"}], "constructors": []}