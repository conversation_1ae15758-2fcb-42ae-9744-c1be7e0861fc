{"doc": "\n 消息类型枚举\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [{"name": "SYSTEM_MESSAGE", "doc": "\n 站内信\r\n"}, {"name": "EMAIL_MESSAGE", "doc": "\n 邮箱\r\n"}, {"name": "SMS_MESSAGE", "doc": "\n 短信\r\n"}], "methods": [{"name": "getByCode", "paramTypes": ["java.lang.String"], "doc": "\n 根据消息类型 code 获取 MessageTypeEnum\r\n\r\n @param code 消息类型code\r\n @return MessageTypeEnum\r\n"}], "constructors": []}