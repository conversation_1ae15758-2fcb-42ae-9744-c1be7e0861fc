{"doc": "\n 导出下拉框Excel示例\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getProvinceList", "paramTypes": [], "doc": "\n 模拟查询数据库操作\r\n\r\n @return /\r\n"}, {"name": "getCityList", "paramTypes": ["java.util.List"], "doc": "\n 模拟查找数据库操作，需要连带查询出省的数据\r\n\r\n @param provinceList 模拟的父省数据\r\n @return /\r\n"}, {"name": "getAreaList", "paramTypes": ["java.util.List"], "doc": "\n 模拟查找数据库操作，需要连带查询出市的数据\r\n\r\n @param cityList 模拟的父市数据\r\n @return /\r\n"}, {"name": "selectParentData", "paramTypes": ["java.util.List", "java.util.List"], "doc": "\n 模拟数据库的查询父数据操作\r\n\r\n @param parentList /\r\n @param sonList    /\r\n"}], "constructors": []}