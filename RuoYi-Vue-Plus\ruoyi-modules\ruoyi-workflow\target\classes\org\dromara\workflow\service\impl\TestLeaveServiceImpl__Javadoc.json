{"doc": "\n 请假Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2023-07-21\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "eval", "paramTypes": ["java.lang.Integer"], "doc": "\n spel条件表达：判断小于2\r\n\r\n @param leaveDays 待判断的变量（可不传自行返回true或false）\r\n @return boolean\r\n"}, {"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询请假\r\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.workflow.domain.bo.TestLeaveBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询请假列表\r\n"}, {"name": "queryList", "paramTypes": ["org.dromara.workflow.domain.bo.TestLeaveBo"], "doc": "\n 查询请假列表\r\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.workflow.domain.bo.TestLeaveBo"], "doc": "\n 新增请假\r\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.workflow.domain.bo.TestLeaveBo"], "doc": "\n 修改请假\r\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.List"], "doc": "\n 批量删除请假\r\n"}, {"name": "processHandler", "paramTypes": ["org.dromara.common.core.domain.event.ProcessEvent"], "doc": "\n 总体流程监听(例如: 草稿，撤销，退回，作废，终止，已完成，单任务完成等)\r\n 正常使用只需#processEvent.flowCode=='leave1'\r\n 示例为了方便则使用startsWith匹配了全部示例key\r\n\r\n @param processEvent 参数\r\n"}, {"name": "processTaskHandler", "paramTypes": ["org.dromara.common.core.domain.event.ProcessTaskEvent"], "doc": "\n 执行任务创建监听\r\n 示例：也可通过  @EventListener(condition = \"#processTaskEvent.flowCode=='leave1'\")进行判断\r\n 在方法中判断流程节点key\r\n if (\"xxx\".equals(processTaskEvent.getNodeCode())) {\r\n //执行业务逻辑\r\n }\r\n\r\n @param processTaskEvent 参数\r\n"}, {"name": "processDeleteHandler", "paramTypes": ["org.dromara.common.core.domain.event.ProcessDeleteEvent"], "doc": "\n 监听删除流程事件\r\n 正常使用只需#processDeleteEvent.flowCode=='leave1'\r\n 示例为了方便则使用startsWith匹配了全部示例key\r\n\r\n @param processDeleteEvent 参数\r\n"}], "constructors": []}