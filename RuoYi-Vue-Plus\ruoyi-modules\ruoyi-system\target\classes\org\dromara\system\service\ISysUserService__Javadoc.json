{"doc": "\n 用户 业务层\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectPageUserList", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 根据条件分页查询用户列表\r\n\r\n @param user      用户信息\r\n @param pageQuery 发呢也\r\n @return 用户信息\r\n"}, {"name": "selectUserExportList", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo"], "doc": "\n 导出用户列表\r\n\r\n @param user 用户信息\r\n @return 用户信息集合信息\r\n"}, {"name": "selectAllocatedList", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 根据条件分页查询已分配用户角色列表\r\n\r\n @param user      用户信息\r\n @param pageQuery 分页\r\n @return 用户信息集合信息\r\n"}, {"name": "selectUnallocatedList", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 根据条件分页查询未分配用户角色列表\r\n\r\n @param user      用户信息\r\n @param pageQuery 分页\r\n @return 用户信息集合信息\r\n"}, {"name": "selectUserByUserName", "paramTypes": ["java.lang.String"], "doc": "\n 通过用户名查询用户\r\n\r\n @param userName 用户名\r\n @return 用户对象信息\r\n"}, {"name": "selectUserByPhonenumber", "paramTypes": ["java.lang.String"], "doc": "\n 通过手机号查询用户\r\n\r\n @param phonenumber 手机号\r\n @return 用户对象信息\r\n"}, {"name": "selectUserById", "paramTypes": ["java.lang.Long"], "doc": "\n 通过用户ID查询用户\r\n\r\n @param userId 用户ID\r\n @return 用户对象信息\r\n"}, {"name": "selectUserByIds", "paramTypes": ["java.util.List", "java.lang.Long"], "doc": "\n 通过用户ID串查询用户\r\n\r\n @param userIds 用户ID串\r\n @param deptId  部门id\r\n @return 用户列表信息\r\n"}, {"name": "selectUserRoleGroup", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID查询用户所属角色组\r\n\r\n @param userId 用户ID\r\n @return 结果\r\n"}, {"name": "selectUserPostGroup", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID查询用户所属岗位组\r\n\r\n @param userId 用户ID\r\n @return 结果\r\n"}, {"name": "checkUserNameUnique", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo"], "doc": "\n 校验用户名称是否唯一\r\n\r\n @param user 用户信息\r\n @return 结果\r\n"}, {"name": "checkPhoneUnique", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo"], "doc": "\n 校验手机号码是否唯一\r\n\r\n @param user 用户信息\r\n @return 结果\r\n"}, {"name": "checkEmailUnique", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo"], "doc": "\n 校验email是否唯一\r\n\r\n @param user 用户信息\r\n @return 结果\r\n"}, {"name": "checkUserAllowed", "paramTypes": ["java.lang.Long"], "doc": "\n 校验用户是否允许操作\r\n\r\n @param userId 用户ID\r\n"}, {"name": "checkUserDataScope", "paramTypes": ["java.lang.Long"], "doc": "\n 校验用户是否有数据权限\r\n\r\n @param userId 用户id\r\n"}, {"name": "insertUser", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo"], "doc": "\n 新增用户信息\r\n\r\n @param user 用户信息\r\n @return 结果\r\n"}, {"name": "registerUser", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo", "java.lang.String"], "doc": "\n 注册用户信息\r\n\r\n @param user 用户信息\r\n @return 结果\r\n"}, {"name": "updateUser", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo"], "doc": "\n 修改用户信息\r\n\r\n @param user 用户信息\r\n @return 结果\r\n"}, {"name": "insertUserAuth", "paramTypes": ["java.lang.Long", "java.lang.Long[]"], "doc": "\n 用户授权角色\r\n\r\n @param userId  用户ID\r\n @param roleIds 角色组\r\n"}, {"name": "updateUserStatus", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 修改用户状态\r\n\r\n @param userId 用户ID\r\n @param status 帐号状态\r\n @return 结果\r\n"}, {"name": "updateUserProfile", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo"], "doc": "\n 修改用户基本信息\r\n\r\n @param user 用户信息\r\n @return 结果\r\n"}, {"name": "updateUserAvatar", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 修改用户头像\r\n\r\n @param userId 用户ID\r\n @param avatar 头像地址\r\n @return 结果\r\n"}, {"name": "resetUserPwd", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 重置用户密码\r\n\r\n @param userId   用户ID\r\n @param password 密码\r\n @return 结果\r\n"}, {"name": "deleteUserById", "paramTypes": ["java.lang.Long"], "doc": "\n 通过用户ID删除用户\r\n\r\n @param userId 用户ID\r\n @return 结果\r\n"}, {"name": "deleteUserByIds", "paramTypes": ["java.lang.Long[]"], "doc": "\n 批量删除用户信息\r\n\r\n @param userIds 需要删除的用户ID\r\n @return 结果\r\n"}, {"name": "selectUserListByDept", "paramTypes": ["java.lang.Long"], "doc": "\n 通过部门id查询当前部门所有用户\r\n\r\n @param deptId 部门id\r\n @return 结果\r\n"}], "constructors": []}