{"doc": "\n 延迟队列 演示案例\r\n <p>\r\n 轻量级队列 重量级数据量 请使用 MQ\r\n 例如: 创建订单30分钟后过期处理\r\n <p>\r\n 集群测试通过 同一个数据只会被消费一次 做好事务补偿\r\n 集群测试流程 两台集群分别开启订阅 在其中一台发送数据 观察接收消息的规律\r\n\r\n <AUTHOR> Li\r\n @version 3.6.0\r\n @deprecated redisson 新版本已经将队列功能标记删除 一些技术问题无法解决 建议搭建MQ使用\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "subscribe", "paramTypes": ["java.lang.String"], "doc": "\n 订阅队列\r\n\r\n @param queueName 队列名\r\n"}, {"name": "add", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Long"], "doc": "\n 添加队列数据\r\n\r\n @param queueName 队列名\r\n @param orderNum  订单号\r\n @param time      延迟时间(秒)\r\n"}, {"name": "remove", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 删除队列数据\r\n\r\n @param queueName 队列名\r\n @param orderNum  订单号\r\n"}, {"name": "destroy", "paramTypes": ["java.lang.String"], "doc": "\n 销毁队列\r\n\r\n @param queueName 队列名\r\n"}], "constructors": []}