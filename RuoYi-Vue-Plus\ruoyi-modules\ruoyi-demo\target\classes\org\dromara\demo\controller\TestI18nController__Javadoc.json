{"doc": "\n 测试国际化\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "get", "paramTypes": ["java.lang.String"], "doc": "\n 通过code获取国际化内容\r\n code为 messages.properties 中的 key\r\n <p>\r\n 测试使用 user.register.success\r\n\r\n @param code 国际化code\r\n"}, {"name": "test1", "paramTypes": ["java.lang.String"], "doc": "\n Validator 校验国际化\r\n 不传值 分别查看异常返回\r\n <p>\r\n 测试使用 not.null\r\n"}, {"name": "test2", "paramTypes": ["org.dromara.demo.controller.TestI18nController.TestI18nBo"], "doc": "\n Bean 校验国际化\r\n 不传值 分别查看异常返回\r\n <p>\r\n 测试使用 not.null\r\n"}], "constructors": []}