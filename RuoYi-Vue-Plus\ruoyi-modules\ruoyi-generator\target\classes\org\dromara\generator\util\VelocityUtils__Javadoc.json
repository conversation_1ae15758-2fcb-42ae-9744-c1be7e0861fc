{"doc": "\n 模板处理工具类\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "PROJECT_PATH", "doc": "\n 项目空间路径\r\n"}, {"name": "MYBATIS_PATH", "doc": "\n mybatis空间路径\r\n"}, {"name": "DEFAULT_PARENT_MENU_ID", "doc": "\n 默认上级菜单，系统工具\r\n"}], "enumConstants": [], "methods": [{"name": "prepareContext", "paramTypes": ["org.dromara.generator.domain.GenTable"], "doc": "\n 设置模板变量信息\r\n\r\n @return 模板列表\r\n"}, {"name": "getTemplateList", "paramTypes": ["java.lang.String"], "doc": "\n 获取模板信息\r\n\r\n @return 模板列表\r\n"}, {"name": "getFileName", "paramTypes": ["java.lang.String", "org.dromara.generator.domain.GenTable"], "doc": "\n 获取文件名\r\n"}, {"name": "getPackagePrefix", "paramTypes": ["java.lang.String"], "doc": "\n 获取包前缀\r\n\r\n @param packageName 包名称\r\n @return 包前缀名称\r\n"}, {"name": "getImportList", "paramTypes": ["org.dromara.generator.domain.GenTable"], "doc": "\n 根据列类型获取导入包\r\n\r\n @param genTable 业务表对象\r\n @return 返回需要导入的包列表\r\n"}, {"name": "getDicts", "paramTypes": ["org.dromara.generator.domain.GenTable"], "doc": "\n 根据列类型获取字典组\r\n\r\n @param genTable 业务表对象\r\n @return 返回字典组\r\n"}, {"name": "addDicts", "paramTypes": ["java.util.Set", "java.util.List"], "doc": "\n 添加字典列表\r\n\r\n @param dicts 字典列表\r\n @param columns 列集合\r\n"}, {"name": "getPermissionPrefix", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 获取权限前缀\r\n\r\n @param moduleName   模块名称\r\n @param businessName 业务名称\r\n @return 返回权限前缀\r\n"}, {"name": "getParentMenuId", "paramTypes": ["cn.hutool.core.lang.Dict"], "doc": "\n 获取上级菜单ID字段\r\n\r\n @param paramsObj 生成其他选项\r\n @return 上级菜单ID字段\r\n"}, {"name": "getTreecode", "paramTypes": ["java.util.Map"], "doc": "\n 获取树编码\r\n\r\n @param paramsObj 生成其他选项\r\n @return 树编码\r\n"}, {"name": "getTreeParentCode", "paramTypes": ["cn.hutool.core.lang.Dict"], "doc": "\n 获取树父编码\r\n\r\n @param paramsObj 生成其他选项\r\n @return 树父编码\r\n"}, {"name": "getTreeName", "paramTypes": ["cn.hutool.core.lang.Dict"], "doc": "\n 获取树名称\r\n\r\n @param paramsObj 生成其他选项\r\n @return 树名称\r\n"}, {"name": "getExpandColumn", "paramTypes": ["org.dromara.generator.domain.GenTable"], "doc": "\n 获取需要在哪一列上面显示展开按钮\r\n\r\n @param genTable 业务表对象\r\n @return 展开按钮列序号\r\n"}], "constructors": []}