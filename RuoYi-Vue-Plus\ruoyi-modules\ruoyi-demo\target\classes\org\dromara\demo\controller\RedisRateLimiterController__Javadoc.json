{"doc": "\n 测试分布式限流样例\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "test", "paramTypes": ["java.lang.String"], "doc": "\n 测试全局限流\r\n 全局影响\r\n"}, {"name": "testip", "paramTypes": ["java.lang.String"], "doc": "\n 测试请求IP限流\r\n 同一IP请求受影响\r\n"}, {"name": "testcluster", "paramTypes": ["java.lang.String"], "doc": "\n 测试集群实例限流\r\n 启动两个后端服务互不影响\r\n"}, {"name": "testObj", "paramTypes": ["java.lang.String"], "doc": "\n 测试请求IP限流(key基于参数获取)\r\n 同一IP请求受影响\r\n\r\n 简单变量获取 #变量 复杂表达式 #{#变量 != 1 ? 1 : 0}\r\n"}], "constructors": []}