{"doc": "\n 对象存储配置\r\n\r\n <AUTHOR>\r\n <AUTHOR>\r\n @date 2021-08-13\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysOssConfigBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询对象存储配置列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 获取对象存储配置详细信息\r\n\r\n @param ossConfigId OSS配置ID\r\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.SysOssConfigBo"], "doc": "\n 新增对象存储配置\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.SysOssConfigBo"], "doc": "\n 修改对象存储配置\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除对象存储配置\r\n\r\n @param ossConfigIds OSS配置ID串\r\n"}, {"name": "changeStatus", "paramTypes": ["org.dromara.system.domain.bo.SysOssConfigBo"], "doc": "\n 状态修改\r\n"}], "constructors": []}