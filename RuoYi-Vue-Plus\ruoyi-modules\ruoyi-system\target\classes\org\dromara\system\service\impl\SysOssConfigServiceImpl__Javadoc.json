{"doc": "\n 对象存储配置Service业务层处理\r\n\r\n <AUTHOR>\r\n <AUTHOR>\r\n @date 2021-08-13\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "init", "paramTypes": [], "doc": "\n 项目启动时，初始化参数到缓存，加载配置类\r\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.system.domain.SysOssConfig"], "doc": "\n 保存前的数据校验\r\n"}, {"name": "checkConfigKeyUnique", "paramTypes": ["org.dromara.system.domain.SysOssConfig"], "doc": "\n 判断configKey是否唯一\r\n"}, {"name": "updateOssConfigStatus", "paramTypes": ["org.dromara.system.domain.bo.SysOssConfigBo"], "doc": "\n 启用禁用状态\r\n"}], "constructors": []}