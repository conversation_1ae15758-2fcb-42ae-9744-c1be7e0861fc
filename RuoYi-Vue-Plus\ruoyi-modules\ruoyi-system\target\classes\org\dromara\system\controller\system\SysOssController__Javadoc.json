{"doc": "\n 文件上传 控制层\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysOssBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询OSS对象存储列表\r\n"}, {"name": "listByIds", "paramTypes": ["java.lang.Long[]"], "doc": "\n 查询OSS对象基于id串\r\n\r\n @param ossIds OSS对象ID串\r\n"}, {"name": "upload", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 上传OSS对象存储\r\n\r\n @param file 文件\r\n"}, {"name": "download", "paramTypes": ["java.lang.Long", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 下载OSS对象\r\n\r\n @param ossId OSS对象ID\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除OSS对象存储\r\n\r\n @param ossIds OSS对象ID串\r\n"}], "constructors": []}