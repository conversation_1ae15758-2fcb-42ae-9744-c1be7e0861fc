{"doc": "\n 数据权限拦截器\r\n\r\n <AUTHOR> Li\r\n @version 3.5.0\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "before<PERSON><PERSON><PERSON>", "paramTypes": ["org.apache.ibatis.executor.Executor", "org.apache.ibatis.mapping.MappedStatement", "java.lang.Object", "org.apache.ibatis.session.RowBounds", "org.apache.ibatis.session.ResultHandler", "org.apache.ibatis.mapping.BoundSql"], "doc": "\n 在执行查询之前，检查并处理数据权限相关逻辑\r\n\r\n @param executor      MyBatis 执行器对象\r\n @param ms            映射语句对象\r\n @param parameter     方法参数\r\n @param rowBounds     分页对象\r\n @param resultHandler 结果处理器\r\n @param boundSql      绑定的 SQL 对象\r\n @throws SQLException 如果发生 SQL 异常\r\n"}, {"name": "beforePrepare", "paramTypes": ["org.apache.ibatis.executor.statement.StatementHandler", "java.sql.Connection", "java.lang.Integer"], "doc": "\n 在准备 SQL 语句之前，检查并处理更新和删除操作的数据权限相关逻辑\r\n\r\n @param sh                 MyBatis StatementHandler 对象\r\n @param connection         数据库连接对象\r\n @param transactionTimeout 事务超时时间\r\n"}, {"name": "processSelect", "paramTypes": ["net.sf.jsqlparser.statement.select.Select", "int", "java.lang.String", "java.lang.Object"], "doc": "\n 处理 SELECT 查询语句中的 WHERE 条件\r\n\r\n @param select SELECT 查询对象\r\n @param index  查询语句的索引\r\n @param sql    查询语句\r\n @param obj    WHERE 条件参数\r\n"}, {"name": "processUpdate", "paramTypes": ["net.sf.jsqlparser.statement.update.Update", "int", "java.lang.String", "java.lang.Object"], "doc": "\n 处理 UPDATE 语句中的 WHERE 条件\r\n\r\n @param update UPDATE 查询对象\r\n @param index  查询语句的索引\r\n @param sql    查询语句\r\n @param obj    WHERE 条件参数\r\n"}, {"name": "processDelete", "paramTypes": ["net.sf.jsqlparser.statement.delete.Delete", "int", "java.lang.String", "java.lang.Object"], "doc": "\n 处理 DELETE 语句中的 WHERE 条件\r\n\r\n @param delete DELETE 查询对象\r\n @param index  查询语句的索引\r\n @param sql    查询语句\r\n @param obj    WHERE 条件参数\r\n"}, {"name": "setWhere", "paramTypes": ["net.sf.jsqlparser.statement.select.PlainSelect", "java.lang.String"], "doc": "\n 设置 SELECT 语句的 WHERE 条件\r\n\r\n @param plainSelect       SELECT 查询对象\r\n @param mappedStatementId 映射语句的 ID\r\n"}, {"name": "buildTableExpression", "paramTypes": ["net.sf.jsqlparser.schema.Table", "net.sf.jsqlparser.expression.Expression", "java.lang.String"], "doc": "\n 构建表达式，用于处理表的数据权限\r\n\r\n @param table        表对象\r\n @param where        WHERE 条件表达式\r\n @param whereSegment WHERE 条件片段\r\n @return 构建的表达式\r\n"}], "constructors": [{"name": "<init>", "paramTypes": ["java.lang.String"], "doc": "\n 构造函数，初始化 PlusDataPermissionHandler 实例\r\n\r\n @param mapperPackage 扫描的映射器包\r\n"}]}