{"doc": "\n 客户端管理\r\n\r\n <AUTHOR>\r\n @date 2023-06-18\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysClientBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询客户端管理列表\r\n"}, {"name": "export", "paramTypes": ["org.dromara.system.domain.bo.SysClientBo", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 导出客户端管理列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 获取客户端管理详细信息\r\n\r\n @param id 主键\r\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.SysClientBo"], "doc": "\n 新增客户端管理\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.SysClientBo"], "doc": "\n 修改客户端管理\r\n"}, {"name": "changeStatus", "paramTypes": ["org.dromara.system.domain.bo.SysClientBo"], "doc": "\n 状态修改\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除客户端管理\r\n\r\n @param ids 主键串\r\n"}], "constructors": []}