{"doc": "\n 流程定义管理 控制层\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.warm.flow.orm.entity.FlowDefinition", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询流程定义列表\r\n\r\n @param flowDefinition 参数\r\n @param pageQuery      分页\r\n"}, {"name": "unPublishList", "paramTypes": ["org.dromara.warm.flow.orm.entity.FlowDefinition", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询未发布的流程定义列表\r\n\r\n @param flowDefinition 参数\r\n @param pageQuery      分页\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 获取流程定义详细信息\r\n\r\n @param id 流程定义id\r\n"}, {"name": "add", "paramTypes": ["org.dromara.warm.flow.orm.entity.FlowDefinition"], "doc": "\n 新增流程定义\r\n\r\n @param flowDefinition 参数\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.warm.flow.orm.entity.FlowDefinition"], "doc": "\n 修改流程定义\r\n\r\n @param flowDefinition 参数\r\n"}, {"name": "publish", "paramTypes": ["java.lang.Long"], "doc": "\n 发布流程定义\r\n\r\n @param id 流程定义id\r\n"}, {"name": "unPublish", "paramTypes": ["java.lang.Long"], "doc": "\n 取消发布流程定义\r\n\r\n @param id 流程定义id\r\n"}, {"name": "remove", "paramTypes": ["java.util.List"], "doc": "\n 删除流程定义\r\n"}, {"name": "copy", "paramTypes": ["java.lang.Long"], "doc": "\n 复制流程定义\r\n\r\n @param id 流程定义id\r\n"}, {"name": "importDef", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.String"], "doc": "\n 导入流程定义\r\n\r\n @param file     文件\r\n @param category 分类\r\n"}, {"name": "exportDef", "paramTypes": ["java.lang.Long", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 导出流程定义\r\n\r\n @param id       流程定义id\r\n @param response 响应\r\n @throws IOException 异常\r\n"}, {"name": "xmlString", "paramTypes": ["java.lang.Long"], "doc": "\n 获取流程定义JSON字符串\r\n\r\n @param id 流程定义id\r\n"}, {"name": "active", "paramTypes": ["java.lang.Long", "boolean"], "doc": "\n 激活/挂起流程定义\r\n\r\n @param id     流程定义id\r\n @param active 激活/挂起\r\n"}], "constructors": []}