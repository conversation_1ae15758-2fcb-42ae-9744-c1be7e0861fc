{"doc": "\n 字典 业务层\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectDictTypeList", "paramTypes": ["org.dromara.system.domain.bo.SysDictTypeBo"], "doc": "\n 根据条件分页查询字典类型\r\n\r\n @param dictType 字典类型信息\r\n @return 字典类型集合信息\r\n"}, {"name": "selectDictTypeAll", "paramTypes": [], "doc": "\n 根据所有字典类型\r\n\r\n @return 字典类型集合信息\r\n"}, {"name": "selectDictDataByType", "paramTypes": ["java.lang.String"], "doc": "\n 根据字典类型查询字典数据\r\n\r\n @param dictType 字典类型\r\n @return 字典数据集合信息\r\n"}, {"name": "selectDictTypeById", "paramTypes": ["java.lang.Long"], "doc": "\n 根据字典类型ID查询信息\r\n\r\n @param dictId 字典类型ID\r\n @return 字典类型\r\n"}, {"name": "selectDictTypeByType", "paramTypes": ["java.lang.String"], "doc": "\n 根据字典类型查询信息\r\n\r\n @param dictType 字典类型\r\n @return 字典类型\r\n"}, {"name": "deleteDictTypeByIds", "paramTypes": ["java.util.List"], "doc": "\n 批量删除字典信息\r\n\r\n @param dictIds 需要删除的字典ID\r\n"}, {"name": "resetDictCache", "paramTypes": [], "doc": "\n 重置字典缓存数据\r\n"}, {"name": "insertDictType", "paramTypes": ["org.dromara.system.domain.bo.SysDictTypeBo"], "doc": "\n 新增保存字典类型信息\r\n\r\n @param bo 字典类型信息\r\n @return 结果\r\n"}, {"name": "updateDictType", "paramTypes": ["org.dromara.system.domain.bo.SysDictTypeBo"], "doc": "\n 修改保存字典类型信息\r\n\r\n @param bo 字典类型信息\r\n @return 结果\r\n"}, {"name": "checkDictTypeUnique", "paramTypes": ["org.dromara.system.domain.bo.SysDictTypeBo"], "doc": "\n 校验字典类型称是否唯一\r\n\r\n @param dictType 字典类型\r\n @return 结果\r\n"}], "constructors": []}