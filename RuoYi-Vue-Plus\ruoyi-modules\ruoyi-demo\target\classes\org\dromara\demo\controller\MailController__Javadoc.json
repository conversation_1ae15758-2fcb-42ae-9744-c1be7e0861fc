{"doc": "\n 邮件发送案例\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "sendSimpleMessage", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 发送邮件\r\n\r\n @param to      接收人\r\n @param subject 标题\r\n @param text    内容\r\n"}, {"name": "sendMessageWithAttachment", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 发送邮件（带附件）\r\n\r\n @param to       接收人\r\n @param subject  标题\r\n @param text     内容\r\n"}, {"name": "sendMessageWithAttachments", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 发送邮件（多附件）\r\n\r\n @param to       接收人\r\n @param subject  标题\r\n @param text     内容\r\n"}], "constructors": []}