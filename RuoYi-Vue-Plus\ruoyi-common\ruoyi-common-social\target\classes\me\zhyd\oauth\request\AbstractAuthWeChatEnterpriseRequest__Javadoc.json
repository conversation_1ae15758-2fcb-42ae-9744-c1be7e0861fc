{"doc": "\n <p>\r\n 企业微信登录父类\r\n </p>\r\n\r\n <AUTHOR> (347826496(a)qq.com)\r\n @since 1.15.9\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "checkResponse", "paramTypes": ["java.lang.String"], "doc": "\n 校验请求结果\r\n\r\n @param response 请求结果\r\n @return 如果请求结果正常，则返回JSONObject\r\n"}, {"name": "accessTokenUrl", "paramTypes": ["java.lang.String"], "doc": "\n 返回获取accessToken的url\r\n\r\n @param code 授权码\r\n @return 返回获取accessToken的url\r\n"}, {"name": "userInfoUrl", "paramTypes": ["me.zhyd.oauth.model.AuthToken"], "doc": "\n 返回获取userInfo的url\r\n\r\n @param authToken 用户授权后的token\r\n @return 返回获取userInfo的url\r\n"}, {"name": "getUserDetail", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 用户详情\r\n\r\n @param accessToken accessToken\r\n @param userId      企业内用户id\r\n @param userTicket  成员票据，用于获取用户信息或敏感信息\r\n @return 用户详情\r\n"}], "constructors": []}