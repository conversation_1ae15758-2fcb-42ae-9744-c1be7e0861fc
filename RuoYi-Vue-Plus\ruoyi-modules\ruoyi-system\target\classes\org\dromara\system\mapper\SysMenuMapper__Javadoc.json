{"doc": "\n 菜单表 数据层\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectMenuListByUserId", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": "\n 根据用户查询系统菜单列表\r\n\r\n @param queryWrapper 查询条件\r\n @return 菜单列表\r\n"}, {"name": "selectMenuPermsByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID查询权限\r\n\r\n @param userId 用户ID\r\n @return 权限列表\r\n"}, {"name": "selectMenuPermsByRoleId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据角色ID查询权限\r\n\r\n @param roleId 角色ID\r\n @return 权限列表\r\n"}, {"name": "selectMenuTreeAll", "paramTypes": [], "doc": "\n 根据用户ID查询菜单\r\n\r\n @return 菜单列表\r\n"}, {"name": "selectMenuTreeByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID查询菜单\r\n\r\n @param userId 用户ID\r\n @return 菜单列表\r\n"}, {"name": "selectMenuListByRoleId", "paramTypes": ["java.lang.Long", "boolean"], "doc": "\n 根据角色ID查询菜单树信息\r\n\r\n @param roleId            角色ID\r\n @param menuCheckStrictly 菜单树选择项是否关联显示\r\n @return 选中菜单列表\r\n"}], "constructors": []}