{"doc": "\n 工具类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "sendMessage", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 向指定的WebSocket会话发送消息\r\n\r\n @param sessionKey 要发送消息的用户id\r\n @param message    要发送的消息内容\r\n"}, {"name": "subscribeMessage", "paramTypes": ["java.util.function.Consumer"], "doc": "\n 订阅WebSocket消息主题，并提供一个消费者函数来处理接收到的消息\r\n\r\n @param consumer 处理WebSocket消息的消费者函数\r\n"}, {"name": "publishMessage", "paramTypes": ["org.dromara.common.websocket.dto.WebSocketMessageDto"], "doc": "\n 发布WebSocket订阅消息\r\n\r\n @param webSocketMessage 要发布的WebSocket消息对象\r\n"}, {"name": "publishAll", "paramTypes": ["java.lang.String"], "doc": "\n 向所有的WebSocket会话发布订阅的消息(群发)\r\n\r\n @param message 要发布的消息内容\r\n"}, {"name": "sendPongMessage", "paramTypes": ["org.springframework.web.socket.WebSocketSession"], "doc": "\n 向指定的WebSocket会话发送Pong消息\r\n\r\n @param session 要发送Pong消息的WebSocket会话\r\n"}, {"name": "sendMessage", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "java.lang.String"], "doc": "\n 向指定的WebSocket会话发送文本消息\r\n\r\n @param session WebSocket会话\r\n @param message 要发送的文本消息内容\r\n"}, {"name": "sendMessage", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "org.springframework.web.socket.WebSocketMessage"], "doc": "\n 向指定的WebSocket会话发送WebSocket消息对象\r\n\r\n @param session WebSocket会话\r\n @param message 要发送的WebSocket消息对象\r\n"}], "constructors": []}