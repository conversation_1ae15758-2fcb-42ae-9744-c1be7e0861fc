{"doc": "\n 代码生成业务字段表 gen_table_column\r\n\r\n <AUTHOR> Li\r\n", "fields": [{"name": "columnId", "doc": "\n 编号\r\n"}, {"name": "tableId", "doc": "\n 归属表编号\r\n"}, {"name": "columnName", "doc": "\n 列名称\r\n"}, {"name": "columnComment", "doc": "\n 列描述\r\n"}, {"name": "columnType", "doc": "\n 列类型\r\n"}, {"name": "javaType", "doc": "\n JAVA类型\r\n"}, {"name": "javaField", "doc": "\n JAVA字段名\r\n"}, {"name": "isPk", "doc": "\n 是否主键（1是）\r\n"}, {"name": "isIncrement", "doc": "\n 是否自增（1是）\r\n"}, {"name": "isRequired", "doc": "\n 是否必填（1是）\r\n"}, {"name": "isInsert", "doc": "\n 是否为插入字段（1是）\r\n"}, {"name": "isEdit", "doc": "\n 是否编辑字段（1是）\r\n"}, {"name": "isList", "doc": "\n 是否列表字段（1是）\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "doc": "\n 是否查询字段（1是）\r\n"}, {"name": "queryType", "doc": "\n 查询方式（EQ等于、NE不等于、GT大于、LT小于、LIKE模糊、BETWEEN范围）\r\n"}, {"name": "htmlType", "doc": "\n 显示类型（input文本框、textarea文本域、select下拉框、checkbox复选框、radio单选框、datetime日期控件、image图片上传控件、upload文件上传控件、editor富文本控件）\r\n"}, {"name": "dictType", "doc": "\n 字典类型\r\n"}, {"name": "sort", "doc": "\n 排序\r\n"}], "enumConstants": [], "methods": [], "constructors": []}