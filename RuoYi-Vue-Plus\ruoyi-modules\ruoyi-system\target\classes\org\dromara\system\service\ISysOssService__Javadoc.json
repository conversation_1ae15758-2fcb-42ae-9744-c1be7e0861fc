{"doc": "\n 文件上传 服务层\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryPageList", "paramTypes": ["org.dromara.system.domain.bo.SysOssBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询OSS对象存储列表\r\n\r\n @param sysOss    OSS对象存储分页查询对象\r\n @param pageQuery 分页查询实体类\r\n @return 结果\r\n"}, {"name": "listByIds", "paramTypes": ["java.util.Collection"], "doc": "\n 根据一组 ossIds 获取对应的 SysOssVo 列表\r\n\r\n @param ossIds 一组文件在数据库中的唯一标识集合\r\n @return 包含 SysOssVo 对象的列表\r\n"}, {"name": "getById", "paramTypes": ["java.lang.Long"], "doc": "\n 根据 ossId 从缓存或数据库中获取 SysOssVo 对象\r\n\r\n @param ossId 文件在数据库中的唯一标识\r\n @return SysOssVo 对象，包含文件信息\r\n"}, {"name": "upload", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 上传 MultipartFile 到对象存储服务，并保存文件信息到数据库\r\n\r\n @param file 要上传的 MultipartFile 对象\r\n @return 上传成功后的 SysOssVo 对象，包含文件信息\r\n"}, {"name": "upload", "paramTypes": ["java.io.File"], "doc": "\n 上传文件到对象存储服务，并保存文件信息到数据库\r\n\r\n @param file 要上传的文件对象\r\n @return 上传成功后的 SysOssVo 对象，包含文件信息\r\n"}, {"name": "download", "paramTypes": ["java.lang.Long", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 文件下载方法，支持一次性下载完整文件\r\n\r\n @param ossId    OSS对象ID\r\n @param response HttpServletResponse对象，用于设置响应头和向客户端发送文件内容\r\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": "\n 删除OSS对象存储\r\n\r\n @param ids     OSS对象ID串\r\n @param isValid 判断是否需要校验\r\n @return 结果\r\n"}], "constructors": []}