{"doc": "\n 客户端管理Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2023-06-18\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询客户端管理\r\n"}, {"name": "queryByClientId", "paramTypes": ["java.lang.String"], "doc": "\n 查询客户端管理\r\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.system.domain.bo.SysClientBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询客户端管理列表\r\n"}, {"name": "queryList", "paramTypes": ["org.dromara.system.domain.bo.SysClientBo"], "doc": "\n 查询客户端管理列表\r\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.system.domain.bo.SysClientBo"], "doc": "\n 新增客户端管理\r\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.system.domain.bo.SysClientBo"], "doc": "\n 修改客户端管理\r\n"}, {"name": "updateClientStatus", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 修改状态\r\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": "\n 批量删除客户端管理\r\n"}], "constructors": []}