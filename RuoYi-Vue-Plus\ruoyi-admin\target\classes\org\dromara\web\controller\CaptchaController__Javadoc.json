{"doc": "\n 验证码操作处理\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "smsCode", "paramTypes": ["java.lang.String"], "doc": "\n 短信验证码\r\n\r\n @param phonenumber 用户手机号\r\n"}, {"name": "emailCode", "paramTypes": ["java.lang.String"], "doc": "\n 邮箱验证码\r\n\r\n @param email 邮箱\r\n"}, {"name": "emailCodeImpl", "paramTypes": ["java.lang.String"], "doc": "\n 邮箱验证码\r\n 独立方法避免验证码关闭之后仍然走限流\r\n"}, {"name": "getCode", "paramTypes": [], "doc": "\n 生成验证码\r\n"}, {"name": "getCodeImpl", "paramTypes": [], "doc": "\n 生成验证码\r\n 独立方法避免验证码关闭之后仍然走限流\r\n"}], "constructors": []}