{"doc": "\n 有界队列 演示案例\r\n <p>\r\n 轻量级队列 重量级数据量 请使用 MQ\r\n <p>\r\n 集群测试通过 同一个数据只会被消费一次 做好事务补偿\r\n 集群测试流程 在其中一台发送数据 两端分别调用获取接口 一次获取一条\r\n\r\n <AUTHOR> Li\r\n @version 3.6.0\r\n @deprecated redisson 新版本已经将队列功能标记删除 一些技术问题无法解决 建议搭建MQ使用\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "add", "paramTypes": ["java.lang.String", "int"], "doc": "\n 添加队列数据\r\n\r\n @param queueName 队列名\r\n @param capacity  容量\r\n"}, {"name": "remove", "paramTypes": ["java.lang.String"], "doc": "\n 删除队列数据\r\n\r\n @param queueName 队列名\r\n"}, {"name": "get", "paramTypes": ["java.lang.String"], "doc": "\n 获取队列数据\r\n\r\n @param queueName 队列名\r\n"}], "constructors": []}