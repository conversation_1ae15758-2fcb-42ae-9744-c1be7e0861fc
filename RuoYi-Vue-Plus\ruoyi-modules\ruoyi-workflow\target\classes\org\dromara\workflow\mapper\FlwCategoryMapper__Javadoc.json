{"doc": "\n 流程分类Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2023-06-27\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "countCategoryById", "paramTypes": ["java.lang.Long"], "doc": "\n 统计指定流程分类ID的分类数量\r\n\r\n @param categoryId 流程分类ID\r\n @return 该流程分类ID的分类数量\r\n"}, {"name": "selectListByParentId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据父流程分类ID查询其所有子流程分类的列表\r\n\r\n @param parentId 父流程分类ID\r\n @return 包含子流程分类的列表\r\n"}, {"name": "selectCategoryIdsByParentId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据父流程分类ID查询包括父ID及其所有子流程分类ID的列表\r\n\r\n @param parentId 父流程分类ID\r\n @return 包含父ID和子流程分类ID的列表\r\n"}], "constructors": []}