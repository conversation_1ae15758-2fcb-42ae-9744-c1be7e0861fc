{"doc": "\n 流程分类对象 wf_category\r\n\r\n <AUTHOR>\r\n @date 2023-06-27\r\n", "fields": [{"name": "categoryId", "doc": "\n 流程分类ID\r\n"}, {"name": "parentId", "doc": "\n 父流程分类id\r\n"}, {"name": "ancestors", "doc": "\n 祖级列表\r\n"}, {"name": "categoryName", "doc": "\n 流程分类名称\r\n"}, {"name": "orderNum", "doc": "\n 显示顺序\r\n"}, {"name": "delFlag", "doc": "\n 删除标志（0代表存在 1代表删除）\r\n"}, {"name": "children", "doc": "\n 子菜单\r\n"}], "enumConstants": [], "methods": [], "constructors": []}