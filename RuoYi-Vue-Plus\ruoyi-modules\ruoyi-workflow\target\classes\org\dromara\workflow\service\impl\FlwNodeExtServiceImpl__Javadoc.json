{"doc": "\n 流程设计器-节点扩展属性\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "CHILD_NODE_MAP", "doc": "\n 存储不同 dictType 对应的配置信息\r\n"}], "enumConstants": [], "methods": [{"name": "getNodeExt", "paramTypes": [], "doc": "\n 获取节点扩展属性\r\n\r\n @return 节点扩展属性列表\r\n"}, {"name": "buildNodeExt", "paramTypes": ["java.lang.String", "java.lang.String", "int", "java.util.List"], "doc": "\n 构建一个 `NodeExt` 对象\r\n\r\n @param code    唯一编码\r\n @param name    名称（新页签时，作为页签名称）\r\n @param type    节点类型（1: 基础设置，2: 新页签）\r\n @param sources 数据来源（枚举类或字典类型）\r\n @return 构建的 `NodeExt` 对象\r\n"}, {"name": "buildChildNode", "paramTypes": ["java.lang.Class"], "doc": "\n 根据枚举类型构建一个 `ChildNode` 对象\r\n\r\n @param enumClass 枚举类，必须实现 `NodeExtEnum` 接口\r\n @return 构建的 `ChildNode` 对象\r\n"}, {"name": "buildChildNode", "paramTypes": ["java.lang.String"], "doc": "\n 根据字典类型构建 `ChildNode` 对象\r\n\r\n @param dictType 字典类型\r\n @return 构建的 `ChildNode` 对象\r\n"}, {"name": "buildChildNodeMap", "paramTypes": ["java.lang.String"], "doc": "\n 根据 CHILD_NODE_MAP 中的配置信息，构建一个基本的 ChildNode 对象\r\n 该方法用于设置 ChildNode 的常规属性，例如 label、type、是否必填、是否多选等\r\n\r\n @param key CHILD_NODE_MAP 的 key\r\n @return 返回构建好的 ChildNode 对象\r\n"}, {"name": "buildButtonPermissionsFromExt", "paramTypes": ["java.lang.String"], "doc": "\n 从扩展属性构建按钮权限列表：根据 ext 中记录的权限值，标记每个按钮是否勾选\r\n\r\n @param ext 扩展属性 JSON 字符串\r\n @return 按钮权限 VO 列表\r\n"}, {"name": "buildPermissionsFromSources", "paramTypes": ["java.util.Map", "java.util.List"], "doc": "\n 将权限映射与按钮权限来源（枚举类或字典类型）进行匹配，生成权限视图列表\r\n <p>\r\n 使用说明：\r\n - sources 支持传入多个来源类型，支持 NodeExtEnum 枚举类 或 字典类型字符串（dictType）\r\n - 若需要扩展更多按钮权限，只需在 sources 中新增对应的枚举类或字典类型\r\n <p>\r\n 示例：\r\n buildPermissionsFromSources(permissionMap, List.of(ButtonPermissionEnum.class, \"custom_button_dict\"));\r\n\r\n @param permissionMap 权限映射\r\n @param sources       枚举类或字典类型列表\r\n @return 按钮权限视图对象列表\r\n"}, {"name": "extractDictItems", "paramTypes": ["org.dromara.warm.flow.ui.vo.NodeExt.ChildNode", "java.util.Set"], "doc": "\n 从节点子项中提取字典项，并构建按钮权限视图对象列表\r\n\r\n @param childNode   子节点\r\n @param selectedSet 已选中的值集\r\n @return 按钮权限视图对象列表\r\n"}], "constructors": []}