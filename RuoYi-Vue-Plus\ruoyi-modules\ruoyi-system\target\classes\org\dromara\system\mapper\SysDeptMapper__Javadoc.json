{"doc": "\n 部门管理 数据层\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectDeptList", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": "\n 查询部门管理数据\r\n\r\n @param queryWrapper 查询条件\r\n @return 部门信息集合\r\n"}, {"name": "selectPageDeptList", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": "\n 分页查询部门管理数据\r\n\r\n @param page         分页信息\r\n @param queryWrapper 查询条件\r\n @return 部门信息集合\r\n"}, {"name": "countDeptById", "paramTypes": ["java.lang.Long"], "doc": "\n 统计指定部门ID的部门数量\r\n\r\n @param deptId 部门ID\r\n @return 该部门ID的部门数量\r\n"}, {"name": "selectListByParentId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据父部门ID查询其所有子部门的列表\r\n\r\n @param parentId 父部门ID\r\n @return 包含子部门的列表\r\n"}, {"name": "selectDeptListByRoleId", "paramTypes": ["java.lang.Long", "boolean"], "doc": "\n 根据角色ID查询部门树信息\r\n\r\n @param roleId            角色ID\r\n @param deptCheckStrictly 部门树选择项是否关联显示\r\n @return 选中部门列表\r\n"}], "constructors": []}