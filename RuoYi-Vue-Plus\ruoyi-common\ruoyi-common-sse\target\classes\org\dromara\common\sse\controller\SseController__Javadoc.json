{"doc": "\n SSE 控制器\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "connect", "paramTypes": [], "doc": "\n 建立 SSE 连接\r\n"}, {"name": "close", "paramTypes": [], "doc": "\n 关闭 SSE 连接\r\n"}, {"name": "send", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 向特定用户发送消息\r\n\r\n @param userId 目标用户的 ID\r\n @param msg    要发送的消息内容\r\n"}, {"name": "send", "paramTypes": ["java.lang.String"], "doc": "\n 向所有用户发送消息\r\n\r\n @param msg 要发送的消息内容\r\n"}, {"name": "destroy", "paramTypes": [], "doc": "\n 清理资源。此方法目前不执行任何操作，但避免因未实现而导致错误\r\n"}], "constructors": []}