{"doc": "\n 授权管理视图对象 sys_client\r\n\r\n <AUTHOR>\r\n @date 2023-05-15\r\n", "fields": [{"name": "id", "doc": "\n id\r\n"}, {"name": "clientId", "doc": "\n 客户端id\r\n"}, {"name": "client<PERSON>ey", "doc": "\n 客户端key\r\n"}, {"name": "clientSecret", "doc": "\n 客户端秘钥\r\n"}, {"name": "grantTypeList", "doc": "\n 授权类型\r\n"}, {"name": "grantType", "doc": "\n 授权类型\r\n"}, {"name": "deviceType", "doc": "\n 设备类型\r\n"}, {"name": "activeTimeout", "doc": "\n token活跃超时时间\r\n"}, {"name": "timeout", "doc": "\n token固定超时时间\r\n"}, {"name": "status", "doc": "\n 状态（0正常 1停用）\r\n"}], "enumConstants": [], "methods": [], "constructors": []}