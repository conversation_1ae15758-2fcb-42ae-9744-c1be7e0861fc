{"doc": "\n 测试单表Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2021-07-26\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "customPageList", "paramTypes": ["org.dromara.demo.domain.bo.TestDemoBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 自定义分页查询\r\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.demo.domain.TestDemo"], "doc": "\n 保存前的数据校验\r\n\r\n @param entity 实体类数据\r\n"}], "constructors": []}