{"doc": "\n 登录校验方法\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "socialRegister", "paramTypes": ["me.zhyd.oauth.model.AuthUser"], "doc": "\n 绑定第三方用户\r\n\r\n @param authUserData 授权响应实体\r\n"}, {"name": "logout", "paramTypes": [], "doc": "\n 退出登录\r\n"}, {"name": "recordLogininfor", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 记录登录信息\r\n\r\n @param tenantId 租户ID\r\n @param username 用户名\r\n @param status   状态\r\n @param message  消息内容\r\n"}, {"name": "buildLoginUser", "paramTypes": ["org.dromara.system.domain.vo.SysUserVo"], "doc": "\n 构建登录用户\r\n"}, {"name": "recordLoginInfo", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 记录登录信息\r\n\r\n @param userId 用户ID\r\n"}, {"name": "checkLogin", "paramTypes": ["org.dromara.common.core.enums.LoginType", "java.lang.String", "java.lang.String", "java.util.function.Supplier"], "doc": "\n 登录校验\r\n"}, {"name": "checkTenant", "paramTypes": ["java.lang.String"], "doc": "\n 校验租户\r\n\r\n @param tenantId 租户ID\r\n"}], "constructors": []}