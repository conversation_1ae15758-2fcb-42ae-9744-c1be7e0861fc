{"doc": "\n 短信演示案例\r\n 请先阅读文档 否则无法使用\r\n\r\n <AUTHOR> Li\r\n @version 4.2.0\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 发送短信Aliyun\r\n\r\n @param phones     电话号\r\n @param templateId 模板ID\r\n"}, {"name": "sendTencent", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 发送短信Tencent\r\n\r\n @param phones     电话号\r\n @param templateId 模板ID\r\n"}, {"name": "addBlacklist", "paramTypes": ["java.lang.String"], "doc": "\n 添加黑名单\r\n\r\n @param phone 手机号\r\n"}, {"name": "removeBlacklist", "paramTypes": ["java.lang.String"], "doc": "\n 移除黑名单\r\n\r\n @param phone 手机号\r\n"}], "constructors": []}