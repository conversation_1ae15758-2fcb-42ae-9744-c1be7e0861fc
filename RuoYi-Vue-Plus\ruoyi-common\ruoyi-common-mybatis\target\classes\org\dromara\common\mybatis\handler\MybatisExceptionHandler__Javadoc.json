{"doc": "\n <PERSON><PERSON><PERSON>异常处理器\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleDuplicateKeyException", "paramTypes": ["org.springframework.dao.DuplicateKeyException", "jakarta.servlet.http.HttpServletRequest"], "doc": "\n 主键或UNIQUE索引，数据重复异常\r\n"}, {"name": "handleCannotFindDataSourceException", "paramTypes": ["org.mybatis.spring.MyBatisSystemException", "jakarta.servlet.http.HttpServletRequest"], "doc": "\n Mybat<PERSON>系统异常 通用处理\r\n"}], "constructors": []}