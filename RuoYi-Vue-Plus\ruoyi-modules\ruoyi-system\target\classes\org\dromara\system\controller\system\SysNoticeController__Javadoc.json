{"doc": "\n 公告 信息操作处理\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysNoticeBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取通知公告列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 根据通知公告编号获取详细信息\r\n\r\n @param noticeId 公告ID\r\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.SysNoticeBo"], "doc": "\n 新增通知公告\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.SysNoticeBo"], "doc": "\n 修改通知公告\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除通知公告\r\n\r\n @param noticeIds 公告ID串\r\n"}], "constructors": []}