{"doc": "\n 操作日志 服务层处理\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "recordOper", "paramTypes": ["org.dromara.common.log.event.OperLogEvent"], "doc": "\n 操作日志记录\r\n\r\n @param operLogEvent 操作日志事件\r\n"}, {"name": "insertOperlog", "paramTypes": ["org.dromara.system.domain.bo.SysOperLogBo"], "doc": "\n 新增操作日志\r\n\r\n @param bo 操作日志对象\r\n"}, {"name": "selectOperLogList", "paramTypes": ["org.dromara.system.domain.bo.SysOperLogBo"], "doc": "\n 查询系统操作日志集合\r\n\r\n @param operLog 操作日志对象\r\n @return 操作日志集合\r\n"}, {"name": "deleteOperLogByIds", "paramTypes": ["java.lang.Long[]"], "doc": "\n 批量删除系统操作日志\r\n\r\n @param operIds 需要删除的操作日志ID\r\n @return 结果\r\n"}, {"name": "selectOperLogById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询操作日志详细\r\n\r\n @param operId 操作ID\r\n @return 操作日志对象\r\n"}, {"name": "cleanOperLog", "paramTypes": [], "doc": "\n 清空操作日志\r\n"}], "constructors": []}