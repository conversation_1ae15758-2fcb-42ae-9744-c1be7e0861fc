{"doc": "\n 分页查询实体类\r\n\r\n <AUTHOR> Li\r\n", "fields": [{"name": "pageSize", "doc": "\n 分页大小\r\n"}, {"name": "pageNum", "doc": "\n 当前页数\r\n"}, {"name": "orderByColumn", "doc": "\n 排序列\r\n"}, {"name": "isAsc", "doc": "\n 排序的方向desc或者asc\r\n"}, {"name": "DEFAULT_PAGE_NUM", "doc": "\n 当前记录起始索引 默认值\r\n"}, {"name": "DEFAULT_PAGE_SIZE", "doc": "\n 每页显示记录数 默认值 默认查全部\r\n"}], "enumConstants": [], "methods": [{"name": "build", "paramTypes": [], "doc": "\n 构建分页对象\r\n"}, {"name": "buildOrderItem", "paramTypes": [], "doc": "\n 构建排序\r\n\r\n 支持的用法如下:\r\n {isAsc:\"asc\",orderByColumn:\"id\"} order by id asc\r\n {isAsc:\"asc\",orderByColumn:\"id,createTime\"} order by id asc,create_time asc\r\n {isAsc:\"desc\",orderByColumn:\"id,createTime\"} order by id desc,create_time desc\r\n {isAsc:\"asc,desc\",orderByColumn:\"id,createTime\"} order by id asc,create_time desc\r\n"}], "constructors": []}