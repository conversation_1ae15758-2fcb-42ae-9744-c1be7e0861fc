{"doc": "\n 部门视图对象 sys_dept\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "deptId", "doc": "\n 部门id\r\n"}, {"name": "parentId", "doc": "\n 父部门id\r\n"}, {"name": "parentName", "doc": "\n 父部门名称\r\n"}, {"name": "ancestors", "doc": "\n 祖级列表\r\n"}, {"name": "deptName", "doc": "\n 部门名称\r\n"}, {"name": "deptCategory", "doc": "\n 部门类别编码\r\n"}, {"name": "orderNum", "doc": "\n 显示顺序\r\n"}, {"name": "leader", "doc": "\n 负责人ID\r\n"}, {"name": "leader<PERSON><PERSON>", "doc": "\n 负责人\r\n"}, {"name": "phone", "doc": "\n 联系电话\r\n"}, {"name": "email", "doc": "\n 邮箱\r\n"}, {"name": "status", "doc": "\n 部门状态（0正常 1停用）\r\n"}, {"name": "createTime", "doc": "\n 创建时间\r\n"}, {"name": "children", "doc": "\n 子部门\r\n"}], "enumConstants": [], "methods": [], "constructors": []}