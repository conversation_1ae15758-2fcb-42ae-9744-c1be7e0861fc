{"doc": "\n 业务 服务层实现\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectGenTableColumnListByTableId", "paramTypes": ["java.lang.Long"], "doc": "\n 查询业务字段列表\r\n\r\n @param tableId 业务字段编号\r\n @return 业务字段集合\r\n"}, {"name": "selectGenTableById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询业务信息\r\n\r\n @param id 业务ID\r\n @return 业务信息\r\n"}, {"name": "selectPageDbTableList", "paramTypes": ["org.dromara.generator.domain.GenTable", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询数据库列表\r\n\r\n @param genTable  包含查询条件的GenTable对象\r\n @param pageQuery 包含分页信息的PageQuery对象\r\n @return 包含分页结果的TableDataInfo对象\r\n"}, {"name": "selectDbTableListByNames", "paramTypes": ["java.lang.String[]", "java.lang.String"], "doc": "\n 查询据库列表\r\n\r\n @param tableNames 表名称组\r\n @param dataName   数据源名称\r\n @return 数据库表集合\r\n"}, {"name": "selectGenTableAll", "paramTypes": [], "doc": "\n 查询所有表信息\r\n\r\n @return 表信息集合\r\n"}, {"name": "updateGenTable", "paramTypes": ["org.dromara.generator.domain.GenTable"], "doc": "\n 修改业务\r\n\r\n @param genTable 业务信息\r\n"}, {"name": "deleteGenTableByIds", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除业务对象\r\n\r\n @param tableIds 需要删除的数据ID\r\n"}, {"name": "importGenTable", "paramTypes": ["java.util.List", "java.lang.String"], "doc": "\n 导入表结构\r\n\r\n @param tableList 导入表列表\r\n @param dataName  数据源名称\r\n"}, {"name": "selectDbTableColumnsByName", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 根据表名称查询列信息\r\n\r\n @param tableName 表名称\r\n @param dataName  数据源名称\r\n @return 列信息\r\n"}, {"name": "previewCode", "paramTypes": ["java.lang.Long"], "doc": "\n 预览代码\r\n\r\n @param tableId 表编号\r\n @return 预览数据列表\r\n"}, {"name": "downloadCode", "paramTypes": ["java.lang.Long"], "doc": "\n 生成代码（下载方式）\r\n\r\n @param tableId 表名称\r\n @return 数据\r\n"}, {"name": "generatorCode", "paramTypes": ["java.lang.Long"], "doc": "\n 生成代码（自定义路径）\r\n\r\n @param tableId 表名称\r\n"}, {"name": "synchDb", "paramTypes": ["java.lang.Long"], "doc": "\n 同步数据库\r\n\r\n @param tableId 表名称\r\n"}, {"name": "downloadCode", "paramTypes": ["java.lang.String[]"], "doc": "\n 批量生成代码（下载方式）\r\n\r\n @param tableIds 表ID数组\r\n @return 数据\r\n"}, {"name": "generatorCode", "paramTypes": ["java.lang.Long", "java.util.zip.ZipOutputStream"], "doc": "\n 查询表信息并生成代码\r\n"}, {"name": "validateEdit", "paramTypes": ["org.dromara.generator.domain.GenTable"], "doc": "\n 修改保存参数校验\r\n\r\n @param genTable 业务信息\r\n"}, {"name": "setPkColumn", "paramTypes": ["org.dromara.generator.domain.GenTable"], "doc": "\n 设置主键列信息\r\n\r\n @param table 业务表信息\r\n"}, {"name": "setTableFromOptions", "paramTypes": ["org.dromara.generator.domain.GenTable"], "doc": "\n 设置代码生成其他选项值\r\n\r\n @param genTable 设置后的生成对象\r\n"}, {"name": "getGenPath", "paramTypes": ["org.dromara.generator.domain.GenTable", "java.lang.String"], "doc": "\n 获取代码生成地址\r\n\r\n @param table    业务表信息\r\n @param template 模板文件路径\r\n @return 生成地址\r\n"}], "constructors": []}