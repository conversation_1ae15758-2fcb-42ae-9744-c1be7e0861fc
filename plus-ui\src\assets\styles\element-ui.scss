.el-collapse {
  .collapse__title {
    font-weight: 600;
    padding: 0 8px;
    font-size: 1.2em;
    line-height: 1.1em;
  }
  .el-collapse-item__content {
    padding: 0 8px;
  }
}

.el-divider--horizontal {
  margin-bottom: 10px;
  margin-top: 10px;
}

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type='file'] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

/*-------------Dialog-------------**/
.el-overlay {
  overflow: hidden;

  .el-overlay-dialog {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;

    .el-dialog {
      margin: 0 auto !important;

      .el-dialog__body {
        padding: 15px !important;
      }
      .el-dialog__header {
        padding: 16px 16px 8px 16px;
        box-sizing: border-box;
        border-bottom: 1px solid var(--brder-color);
        margin-right: 0;
      }
    }
  }
}

.el-dialog__body {
  max-height: calc(90vh - 111px) !important;
  overflow-y: auto;
  overflow-x: hidden;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-menu--collapse > div > .el-submenu > .el-submenu__title .el-submenu__icon-arrow {
  display: none;
}

.el-dropdown .el-dropdown-link {
  color: var(--el-color-primary) !important;
}

/* 当 el-form 的 inline 属性为 true 时 */
/* 设置 label 的宽度默认为 68px */
.el-form--inline .el-form-item__label {
  width: 68px;
}

/* 设置 el-select 的宽度默认为 240px */
.el-form--inline .el-select {
  width: 240px;
}

/* 设置 el-input 的宽度默认为 240px */
.el-form--inline .el-input {
  width: 240px;
}

/* 设置 el-message-box 消息弹框内容强制换行 */
.el-message-box .el-message-box__message {
  word-break: break-word;
}
