{"doc": "\n 业务表 gen_table\r\n\r\n <AUTHOR> Li\r\n", "fields": [{"name": "tableId", "doc": "\n 编号\r\n"}, {"name": "dataName", "doc": "\n 数据源名称\r\n"}, {"name": "tableName", "doc": "\n 表名称\r\n"}, {"name": "tableComment", "doc": "\n 表描述\r\n"}, {"name": "subTableName", "doc": "\n 关联父表的表名\r\n"}, {"name": "subTableFkName", "doc": "\n 本表关联父表的外键名\r\n"}, {"name": "className", "doc": "\n 实体类名称(首字母大写)\r\n"}, {"name": "tplCategory", "doc": "\n 使用的模板（crud单表操作 tree树表操作 sub主子表操作）\r\n"}, {"name": "packageName", "doc": "\n 生成包路径\r\n"}, {"name": "moduleName", "doc": "\n 生成模块名\r\n"}, {"name": "businessName", "doc": "\n 生成业务名\r\n"}, {"name": "functionName", "doc": "\n 生成功能名\r\n"}, {"name": "function<PERSON><PERSON>or", "doc": "\n 生成作者\r\n"}, {"name": "genType", "doc": "\n 生成代码方式（0zip压缩包 1自定义路径）\r\n"}, {"name": "gen<PERSON><PERSON>", "doc": "\n 生成路径（不填默认项目路径）\r\n"}, {"name": "pkColumn", "doc": "\n 主键信息\r\n"}, {"name": "columns", "doc": "\n 表列信息\r\n"}, {"name": "options", "doc": "\n 其它生成选项\r\n"}, {"name": "remark", "doc": "\n 备注\r\n"}, {"name": "treeCode", "doc": "\n 树编码字段\r\n"}, {"name": "treeParentCode", "doc": "\n 树父编码字段\r\n"}, {"name": "treeName", "doc": "\n 树名称字段\r\n"}, {"name": "parentMenuId", "doc": "\n 上级菜单ID字段\r\n"}, {"name": "parentMenuName", "doc": "\n 上级菜单名称字段\r\n"}], "enumConstants": [], "methods": [], "constructors": []}