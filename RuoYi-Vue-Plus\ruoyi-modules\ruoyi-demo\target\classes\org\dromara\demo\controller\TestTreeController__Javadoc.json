{"doc": "\n 测试树表Controller\r\n\r\n <AUTHOR>\r\n @date 2021-07-26\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.demo.domain.bo.TestTreeBo"], "doc": "\n 查询测试树表列表\r\n"}, {"name": "export", "paramTypes": ["org.dromara.demo.domain.bo.TestTreeBo", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 导出测试树表列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 获取测试树表详细信息\r\n\r\n @param id 测试树ID\r\n"}, {"name": "add", "paramTypes": ["org.dromara.demo.domain.bo.TestTreeBo"], "doc": "\n 新增测试树表\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.demo.domain.bo.TestTreeBo"], "doc": "\n 修改测试树表\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除测试树表\r\n\r\n @param ids 测试树ID串\r\n"}], "constructors": []}