{"doc": "\n 操作日志记录表 oper_log\r\n\r\n <AUTHOR> Li\r\n", "fields": [{"name": "operId", "doc": "\n 日志主键\r\n"}, {"name": "tenantId", "doc": "\n 租户编号\r\n"}, {"name": "title", "doc": "\n 操作模块\r\n"}, {"name": "businessType", "doc": "\n 业务类型（0其它 1新增 2修改 3删除）\r\n"}, {"name": "method", "doc": "\n 请求方法\r\n"}, {"name": "requestMethod", "doc": "\n 请求方式\r\n"}, {"name": "operatorType", "doc": "\n 操作类别（0其它 1后台用户 2手机端用户）\r\n"}, {"name": "operName", "doc": "\n 操作人员\r\n"}, {"name": "deptName", "doc": "\n 部门名称\r\n"}, {"name": "operUrl", "doc": "\n 请求url\r\n"}, {"name": "operIp", "doc": "\n 操作地址\r\n"}, {"name": "operLocation", "doc": "\n 操作地点\r\n"}, {"name": "operParam", "doc": "\n 请求参数\r\n"}, {"name": "jsonResult", "doc": "\n 返回参数\r\n"}, {"name": "status", "doc": "\n 操作状态（0正常 1异常）\r\n"}, {"name": "errorMsg", "doc": "\n 错误消息\r\n"}, {"name": "operTime", "doc": "\n 操作时间\r\n"}, {"name": "costTime", "doc": "\n 消耗时间\r\n"}], "enumConstants": [], "methods": [], "constructors": []}