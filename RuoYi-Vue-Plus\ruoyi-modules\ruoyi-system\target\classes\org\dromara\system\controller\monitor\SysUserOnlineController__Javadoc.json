{"doc": "\n 在线用户监控\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 获取在线用户监控列表\r\n\r\n @param ipaddr   IP地址\r\n @param userName 用户名\r\n"}, {"name": "forceLogout", "paramTypes": ["java.lang.String"], "doc": "\n 强退用户\r\n\r\n @param tokenId token值\r\n"}, {"name": "getInfo", "paramTypes": [], "doc": "\n 获取当前用户登录在线设备\r\n"}, {"name": "remove", "paramTypes": ["java.lang.String"], "doc": "\n 强退当前在线设备\r\n\r\n @param tokenId token值\r\n"}], "constructors": []}