{"doc": "\n 操作日志记录处理\r\n\r\n <AUTHOR> Li\r\n", "fields": [{"name": "EXCLUDE_PROPERTIES", "doc": "\n 排除敏感属性字段\r\n"}, {"name": "KEY_CACHE", "doc": "\n 计时 key\r\n"}], "enumConstants": [], "methods": [{"name": "doBefore", "paramTypes": ["org.aspectj.lang.JoinPoint", "org.dromara.common.log.annotation.Log"], "doc": "\n 处理请求前执行\r\n"}, {"name": "doAfterReturning", "paramTypes": ["org.aspectj.lang.JoinPoint", "org.dromara.common.log.annotation.Log", "java.lang.Object"], "doc": "\n 处理完请求后执行\r\n\r\n @param joinPoint 切点\r\n"}, {"name": "doAfterThrowing", "paramTypes": ["org.aspectj.lang.JoinPoint", "org.dromara.common.log.annotation.Log", "java.lang.Exception"], "doc": "\n 拦截异常操作\r\n\r\n @param joinPoint 切点\r\n @param e         异常\r\n"}, {"name": "getControllerMethodDescription", "paramTypes": ["org.aspectj.lang.JoinPoint", "org.dromara.common.log.annotation.Log", "org.dromara.common.log.event.OperLogEvent", "java.lang.Object"], "doc": "\n 获取注解中对方法的描述信息 用于Controller层注解\r\n\r\n @param log     日志\r\n @param operLog 操作日志\r\n @throws Exception\r\n"}, {"name": "setRequestValue", "paramTypes": ["org.aspectj.lang.JoinPoint", "org.dromara.common.log.event.OperLogEvent", "java.lang.String[]"], "doc": "\n 获取请求的参数，放到log中\r\n\r\n @param operLog 操作日志\r\n @throws Exception 异常\r\n"}, {"name": "argsArrayToString", "paramTypes": ["java.lang.Object[]", "java.lang.String[]"], "doc": "\n 参数拼装\r\n"}, {"name": "isFilterObject", "paramTypes": ["java.lang.Object"], "doc": "\n 判断是否需要过滤的对象。\r\n\r\n @param o 对象信息。\r\n @return 如果是需要过滤的对象，则返回true；否则返回false。\r\n"}], "constructors": []}