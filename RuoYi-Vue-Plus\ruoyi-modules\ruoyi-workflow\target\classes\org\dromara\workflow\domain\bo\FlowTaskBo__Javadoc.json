{"doc": "\n 任务请求对象\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "nodeName", "doc": "\n 任务名称\r\n"}, {"name": "flowName", "doc": "\n 流程定义名称\r\n"}, {"name": "flowCode", "doc": "\n 流程定义编码\r\n"}, {"name": "category", "doc": "\n 流程分类id\r\n"}, {"name": "instanceId", "doc": "\n 流程实例id\r\n"}, {"name": "permissionList", "doc": "\n 权限列表\r\n"}, {"name": "createByIds", "doc": "\n 申请人Ids\r\n"}], "enumConstants": [], "methods": [], "constructors": []}