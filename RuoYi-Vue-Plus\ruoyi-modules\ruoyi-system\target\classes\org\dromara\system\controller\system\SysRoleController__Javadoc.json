{"doc": "\n 角色信息\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取角色信息列表\r\n"}, {"name": "export", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 导出角色信息列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 根据角色编号获取详细信息\r\n\r\n @param roleId 角色ID\r\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo"], "doc": "\n 新增角色\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo"], "doc": "\n 修改保存角色\r\n"}, {"name": "dataScope", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo"], "doc": "\n 修改保存数据权限\r\n"}, {"name": "changeStatus", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo"], "doc": "\n 状态修改\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除角色\r\n\r\n @param roleIds 角色ID串\r\n"}, {"name": "optionselect", "paramTypes": ["java.lang.Long[]"], "doc": "\n 获取角色选择框列表\r\n\r\n @param roleIds 角色ID串\r\n"}, {"name": "allocatedList", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询已分配用户角色列表\r\n"}, {"name": "unallocatedList", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询未分配用户角色列表\r\n"}, {"name": "cancelAuthUser", "paramTypes": ["org.dromara.system.domain.SysUserRole"], "doc": "\n 取消授权用户\r\n"}, {"name": "cancelAuthUserAll", "paramTypes": ["java.lang.Long", "java.lang.Long[]"], "doc": "\n 批量取消授权用户\r\n\r\n @param roleId  角色ID\r\n @param userIds 用户ID串\r\n"}, {"name": "selectAuthUserAll", "paramTypes": ["java.lang.Long", "java.lang.Long[]"], "doc": "\n 批量选择用户授权\r\n\r\n @param roleId  角色ID\r\n @param userIds 用户ID串\r\n"}, {"name": "roleDeptTreeselect", "paramTypes": ["java.lang.Long"], "doc": "\n 获取对应角色部门树列表\r\n\r\n @param roleId 角色ID\r\n"}], "constructors": []}