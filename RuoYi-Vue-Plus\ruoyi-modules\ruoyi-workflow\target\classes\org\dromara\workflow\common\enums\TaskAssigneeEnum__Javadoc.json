{"doc": "\n 任务分配人枚举\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [{"name": "USER", "doc": "\n 用户\r\n"}, {"name": "ROLE", "doc": "\n 角色\r\n"}, {"name": "DEPT", "doc": "\n 部门\r\n"}, {"name": "POST", "doc": "\n 岗位\r\n"}], "methods": [{"name": "fromDesc", "paramTypes": ["java.lang.String"], "doc": "\n 根据描述获取对应的枚举类型\r\n <p>\r\n 通过传入描述，查找并返回匹配的枚举项。如果未找到匹配项，会抛出 {@link ServiceException}。\r\n </p>\r\n\r\n @param desc 描述，用于匹配对应的枚举项\r\n @return TaskAssigneeEnum 返回对应的枚举类型\r\n @throws ServiceException 如果未找到匹配的枚举项\r\n"}, {"name": "fromCode", "paramTypes": ["java.lang.String"], "doc": "\n 根据代码获取对应的枚举类型\r\n <p>\r\n 通过传入代码，查找并返回匹配的枚举项。如果未找到匹配项，会抛出 {@link ServiceException}。\r\n </p>\r\n\r\n @param code 代码，用于匹配对应的枚举项\r\n @return TaskAssigneeEnum 返回对应的枚举类型\r\n @throws IllegalArgumentException 如果未找到匹配的枚举项\r\n"}, {"name": "getAssigneeTypeList", "paramTypes": [], "doc": "\n 获取所有办理人类型的描述列表\r\n <p>\r\n 获取当前枚举类所有项的描述字段列表，通常用于展示选择项。\r\n </p>\r\n\r\n @return List<String> 返回所有办理人类型的描述列表\r\n"}, {"name": "getAssigneeCodeList", "paramTypes": [], "doc": "\n 获取所有办理人类型的代码列表\r\n <p>\r\n 获取当前枚举类所有项的代码字段列表，通常用于程序内部逻辑的判断。\r\n </p>\r\n\r\n @return List<String> 返回所有办理人类型的代码列表\r\n"}], "constructors": []}