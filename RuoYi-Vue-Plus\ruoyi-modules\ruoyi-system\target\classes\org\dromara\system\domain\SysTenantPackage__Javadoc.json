{"doc": "\n 租户套餐对象 sys_tenant_package\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "packageId", "doc": "\n 租户套餐id\r\n"}, {"name": "packageName", "doc": "\n 套餐名称\r\n"}, {"name": "menuIds", "doc": "\n 关联菜单id\r\n"}, {"name": "remark", "doc": "\n 备注\r\n"}, {"name": "menu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doc": "\n 菜单树选择项是否关联显示（ 0：父子不互相关联显示 1：父子互相关联显示）\r\n"}, {"name": "status", "doc": "\n 状态（0正常 1停用）\r\n"}, {"name": "delFlag", "doc": "\n 删除标志（0代表存在 1代表删除）\r\n"}], "enumConstants": [], "methods": [], "constructors": []}