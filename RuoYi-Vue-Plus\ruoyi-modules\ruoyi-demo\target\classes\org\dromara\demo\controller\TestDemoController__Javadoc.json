{"doc": "\n 测试单表Controller\r\n\r\n <AUTHOR>\r\n @date 2021-07-26\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.demo.domain.bo.TestDemoBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询测试单表列表\r\n"}, {"name": "page", "paramTypes": ["org.dromara.demo.domain.bo.TestDemoBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 自定义分页查询\r\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 导入数据\r\n\r\n @param file 导入文件\r\n"}, {"name": "export", "paramTypes": ["org.dromara.demo.domain.bo.TestDemoBo", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 导出测试单表列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 获取测试单表详细信息\r\n\r\n @param id 测试ID\r\n"}, {"name": "add", "paramTypes": ["org.dromara.demo.domain.bo.TestDemoBo"], "doc": "\n 新增测试单表\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.demo.domain.bo.TestDemoBo"], "doc": "\n 修改测试单表\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除测试单表\r\n\r\n @param ids 测试ID串\r\n"}], "constructors": []}