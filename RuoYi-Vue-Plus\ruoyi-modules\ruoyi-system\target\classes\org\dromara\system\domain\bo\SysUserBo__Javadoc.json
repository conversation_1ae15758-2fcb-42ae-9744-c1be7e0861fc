{"doc": "\n 用户信息业务对象 sys_user\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "deptId", "doc": "\n 部门ID\r\n"}, {"name": "userName", "doc": "\n 用户账号\r\n"}, {"name": "nick<PERSON><PERSON>", "doc": "\n 用户昵称\r\n"}, {"name": "userType", "doc": "\n 用户类型（sys_user系统用户）\r\n"}, {"name": "email", "doc": "\n 用户邮箱\r\n"}, {"name": "phonenumber", "doc": "\n 手机号码\r\n"}, {"name": "sex", "doc": "\n 用户性别（0男 1女 2未知）\r\n"}, {"name": "password", "doc": "\n 密码\r\n"}, {"name": "status", "doc": "\n 帐号状态（0正常 1停用）\r\n"}, {"name": "remark", "doc": "\n 备注\r\n"}, {"name": "roleIds", "doc": "\n 角色组\r\n"}, {"name": "postIds", "doc": "\n 岗位组\r\n"}, {"name": "roleId", "doc": "\n 数据权限 当前角色ID\r\n"}, {"name": "userIds", "doc": "\n 用户ID\r\n"}, {"name": "excludeUserIds", "doc": "\n 排除不查询的用户(工作流用)\r\n"}], "enumConstants": [], "methods": [], "constructors": []}