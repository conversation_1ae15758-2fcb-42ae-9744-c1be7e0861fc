{"doc": "\n 管理 Server-Sent Events (SSE) 连接\r\n\r\n <AUTHOR> Li\r\n", "fields": [{"name": "SSE_TOPIC", "doc": "\n 订阅的频道\r\n"}], "enumConstants": [], "methods": [{"name": "connect", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 建立与指定用户的 SSE 连接\r\n\r\n @param userId 用户的唯一标识符，用于区分不同用户的连接\r\n @param token  用户的唯一令牌，用于识别具体的连接\r\n @return 返回一个 SseEmitter 实例，客户端可以通过该实例接收 SSE 事件\r\n"}, {"name": "disconnect", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 断开指定用户的 SSE 连接\r\n\r\n @param userId 用户的唯一标识符，用于区分不同用户的连接\r\n @param token  用户的唯一令牌，用于识别具体的连接\r\n"}, {"name": "subscribeMessage", "paramTypes": ["java.util.function.Consumer"], "doc": "\n 订阅SSE消息主题，并提供一个消费者函数来处理接收到的消息\r\n\r\n @param consumer 处理SSE消息的消费者函数\r\n"}, {"name": "sendMessage", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 向指定的用户会话发送消息\r\n\r\n @param userId  要发送消息的用户id\r\n @param message 要发送的消息内容\r\n"}, {"name": "sendMessage", "paramTypes": ["java.lang.String"], "doc": "\n 本机全用户会话发送消息\r\n\r\n @param message 要发送的消息内容\r\n"}, {"name": "publishMessage", "paramTypes": ["org.dromara.common.sse.dto.SseMessageDto"], "doc": "\n 发布SSE订阅消息\r\n\r\n @param sseMessageDto 要发布的SSE消息对象\r\n"}, {"name": "publishAll", "paramTypes": ["java.lang.String"], "doc": "\n 向所有的用户发布订阅的消息(群发)\r\n\r\n @param message 要发布的消息内容\r\n"}], "constructors": []}