{"doc": "\n 租户套餐管理\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysTenantPackageBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询租户套餐列表\r\n"}, {"name": "selectList", "paramTypes": [], "doc": "\n 查询租户套餐下拉选列表\r\n"}, {"name": "export", "paramTypes": ["org.dromara.system.domain.bo.SysTenantPackageBo", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 导出租户套餐列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 获取租户套餐详细信息\r\n\r\n @param packageId 主键\r\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.SysTenantPackageBo"], "doc": "\n 新增租户套餐\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.SysTenantPackageBo"], "doc": "\n 修改租户套餐\r\n"}, {"name": "changeStatus", "paramTypes": ["org.dromara.system.domain.bo.SysTenantPackageBo"], "doc": "\n 状态修改\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除租户套餐\r\n\r\n @param packageIds 主键串\r\n"}], "constructors": []}