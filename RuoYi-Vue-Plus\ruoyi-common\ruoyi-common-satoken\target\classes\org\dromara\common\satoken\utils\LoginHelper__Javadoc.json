{"doc": "\n 登录鉴权助手\r\n <p>\r\n user_type 为 用户类型 同一个用户表 可以有多种用户类型 例如 pc,app\r\n deivce 为 设备类型 同一个用户类型 可以有 多种设备类型 例如 web,ios\r\n 可以组成 用户类型与设备类型多对多的 权限灵活控制\r\n <p>\r\n 多用户体系 针对 多种用户类型 但权限控制不一致\r\n 可以组成 多用户类型表与多设备类型 分别控制权限\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "login", "paramTypes": ["org.dromara.common.core.domain.model.LoginUser", "cn.dev33.satoken.stp.parameter.SaLoginParameter"], "doc": "\n 登录系统 基于 设备类型\r\n 针对相同用户体系不同设备\r\n\r\n @param loginUser 登录用户信息\r\n @param model     配置参数\r\n"}, {"name": "getLoginUser", "paramTypes": [], "doc": "\n 获取用户(多级缓存)\r\n"}, {"name": "getLoginUser", "paramTypes": ["java.lang.String"], "doc": "\n 获取用户基于token\r\n"}, {"name": "getUserId", "paramTypes": [], "doc": "\n 获取用户id\r\n"}, {"name": "getUserIdStr", "paramTypes": [], "doc": "\n 获取用户id\r\n"}, {"name": "getUsername", "paramTypes": [], "doc": "\n 获取用户账户\r\n"}, {"name": "getTenantId", "paramTypes": [], "doc": "\n 获取租户ID\r\n"}, {"name": "getDeptId", "paramTypes": [], "doc": "\n 获取部门ID\r\n"}, {"name": "getDeptName", "paramTypes": [], "doc": "\n 获取部门名\r\n"}, {"name": "getDeptCategory", "paramTypes": [], "doc": "\n 获取部门类别编码\r\n"}, {"name": "getExtra", "paramTypes": ["java.lang.String"], "doc": "\n 获取当前 Token 的扩展信息\r\n\r\n @param key 键值\r\n @return 对应的扩展数据\r\n"}, {"name": "getUserType", "paramTypes": [], "doc": "\n 获取用户类型\r\n"}, {"name": "isSuperAdmin", "paramTypes": ["java.lang.Long"], "doc": "\n 是否为超级管理员\r\n\r\n @param userId 用户ID\r\n @return 结果\r\n"}, {"name": "isSuperAdmin", "paramTypes": [], "doc": "\n 是否为超级管理员\r\n\r\n @return 结果\r\n"}, {"name": "isTenantAdmin", "paramTypes": ["java.util.Set"], "doc": "\n 是否为租户管理员\r\n\r\n @param rolePermission 角色权限标识组\r\n @return 结果\r\n"}, {"name": "isTenantAdmin", "paramTypes": [], "doc": "\n 是否为租户管理员\r\n\r\n @return 结果\r\n"}, {"name": "is<PERSON>ogin", "paramTypes": [], "doc": "\n 检查当前用户是否已登录\r\n\r\n @return 结果\r\n"}], "constructors": []}