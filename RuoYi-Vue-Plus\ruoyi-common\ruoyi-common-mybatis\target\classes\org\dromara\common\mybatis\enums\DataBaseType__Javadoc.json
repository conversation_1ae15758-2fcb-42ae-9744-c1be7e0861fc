{"doc": "\n 数据库类型\r\n\r\n <AUTHOR> Li\r\n", "fields": [{"name": "type", "doc": "\n 数据库类型\r\n"}], "enumConstants": [{"name": "MY_SQL", "doc": "\n MySQL\r\n"}, {"name": "ORACLE", "doc": "\n Oracle\r\n"}, {"name": "POSTGRE_SQL", "doc": "\n PostgreSQL\r\n"}, {"name": "SQL_SERVER", "doc": "\n SQL Server\r\n"}], "methods": [{"name": "find", "paramTypes": ["java.lang.String"], "doc": "\n 根据数据库产品名称查找对应的数据库类型\r\n\r\n @param databaseProductName 数据库产品名称\r\n @return 对应的数据库类型枚举值，如果未找到则返回 null\r\n"}], "constructors": []}