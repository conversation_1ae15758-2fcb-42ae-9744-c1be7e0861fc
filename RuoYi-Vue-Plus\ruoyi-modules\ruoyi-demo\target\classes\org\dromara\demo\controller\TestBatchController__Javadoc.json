{"doc": "\n 测试批量方法\r\n\r\n <AUTHOR>\r\n @date 2021-05-30\r\n", "fields": [{"name": "testDemoMapper", "doc": "\n 为了便于测试 直接引入mapper\r\n"}], "enumConstants": [], "methods": [{"name": "add", "paramTypes": [], "doc": "\n 新增批量方法 可完美替代 saveBatch 秒级插入上万数据 (对mysql负荷较大)\r\n <p>\r\n 3.5.0 版本 增加 rewriteBatchedStatements=true 批处理参数 使 MP 原生批处理可以达到同样的速度\r\n"}, {"name": "addOrUpdate", "paramTypes": [], "doc": "\n 新增或更新 可完美替代 saveOrUpdateBatch 高性能\r\n <p>\r\n 3.5.0 版本 增加 rewriteBatchedStatements=true 批处理参数 使 MP 原生批处理可以达到同样的速度\r\n"}, {"name": "remove", "paramTypes": [], "doc": "\n 删除批量方法\r\n"}], "constructors": []}