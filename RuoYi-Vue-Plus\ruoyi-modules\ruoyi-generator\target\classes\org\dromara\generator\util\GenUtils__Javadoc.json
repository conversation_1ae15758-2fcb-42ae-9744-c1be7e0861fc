{"doc": "\n 代码生成器 工具类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "initTable", "paramTypes": ["org.dromara.generator.domain.GenTable"], "doc": "\n 初始化表信息\r\n"}, {"name": "initColumnField", "paramTypes": ["org.dromara.generator.domain.GenTableColumn", "org.dromara.generator.domain.GenTable"], "doc": "\n 初始化列属性字段\r\n"}, {"name": "arraysContains", "paramTypes": ["java.lang.String[]", "java.lang.String"], "doc": "\n 校验数组是否包含指定值\r\n\r\n @param arr         数组\r\n @param targetValue 值\r\n @return 是否包含\r\n"}, {"name": "getModuleName", "paramTypes": ["java.lang.String"], "doc": "\n 获取模块名\r\n\r\n @param packageName 包名\r\n @return 模块名\r\n"}, {"name": "getBusinessName", "paramTypes": ["java.lang.String"], "doc": "\n 获取业务名\r\n\r\n @param tableName 表名\r\n @return 业务名\r\n"}, {"name": "convertClassName", "paramTypes": ["java.lang.String"], "doc": "\n 表名转换成Java类名\r\n\r\n @param tableName 表名称\r\n @return 类名\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "java.lang.String[]"], "doc": "\n 批量替换前缀\r\n\r\n @param replacementm 替换值\r\n @param searchList   替换列表\r\n"}, {"name": "replaceText", "paramTypes": ["java.lang.String"], "doc": "\n 关键字替换\r\n\r\n @param text 需要被替换的名字\r\n @return 替换后的名字\r\n"}, {"name": "getDbType", "paramTypes": ["java.lang.String"], "doc": "\n 获取数据库类型字段\r\n\r\n @param columnType 列类型\r\n @return 截取后的列类型\r\n"}, {"name": "getColumnLength", "paramTypes": ["java.lang.String"], "doc": "\n 获取字段长度\r\n\r\n @param columnType 列类型\r\n @return 截取后的列类型\r\n"}], "constructors": []}