{"doc": "\n WebSocketSession 用于保存当前所有在线的会话信息\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "addSession", "paramTypes": ["java.lang.Long", "org.springframework.web.socket.WebSocketSession"], "doc": "\n 将WebSocket会话添加到用户会话Map中\r\n\r\n @param sessionKey 会话键，用于检索会话\r\n @param session    要添加的WebSocket会话\r\n"}, {"name": "removeSession", "paramTypes": ["java.lang.Long"], "doc": "\n 从用户会话Map中移除指定会话键对应的WebSocket会话\r\n\r\n @param sessionKey 要移除的会话键\r\n"}, {"name": "getSessions", "paramTypes": ["java.lang.Long"], "doc": "\n 根据会话键从用户会话Map中获取WebSocket会话\r\n\r\n @param sessionKey 要获取的会话键\r\n @return 与给定会话键对应的WebSocket会话，如果不存在则返回null\r\n"}, {"name": "getSessionsAll", "paramTypes": [], "doc": "\n 获取存储在用户会话Map中所有WebSocket会话的会话键集合\r\n\r\n @return 所有WebSocket会话的会话键集合\r\n"}, {"name": "existSession", "paramTypes": ["java.lang.Long"], "doc": "\n 检查给定的会话键是否存在于用户会话Map中\r\n\r\n @param sessionKey 要检查的会话键\r\n @return 如果存在对应的会话键，则返回true；否则返回false\r\n"}], "constructors": []}