{"doc": "\n 无符号计算生成器\r\n\r\n <AUTHOR> Li\r\n", "fields": [{"name": "numberLength", "doc": "\n 参与计算数字最大长度\r\n"}], "enumConstants": [], "methods": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": [], "doc": "\n 获取验证码长度\r\n\r\n @return 验证码长度\r\n"}, {"name": "getLimit", "paramTypes": [], "doc": "\n 根据长度获取参与计算数字最大值\r\n\r\n @return 最大值\r\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": "\n 构造\r\n"}, {"name": "<init>", "paramTypes": ["int"], "doc": "\n 构造\r\n\r\n @param numberLength 参与计算最大数字位数\r\n"}]}