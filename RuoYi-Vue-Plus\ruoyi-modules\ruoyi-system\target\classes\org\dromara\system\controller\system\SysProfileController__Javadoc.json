{"doc": "\n 个人信息 业务处理\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "profile", "paramTypes": [], "doc": "\n 个人信息\r\n"}, {"name": "updateProfile", "paramTypes": ["org.dromara.system.domain.bo.SysUserProfileBo"], "doc": "\n 修改用户信息\r\n"}, {"name": "updatePwd", "paramTypes": ["org.dromara.system.domain.bo.SysUserPasswordBo"], "doc": "\n 重置密码\r\n\r\n @param bo 新旧密码\r\n"}, {"name": "avatar", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 头像上传\r\n\r\n @param avatarfile 用户头像\r\n"}], "constructors": []}