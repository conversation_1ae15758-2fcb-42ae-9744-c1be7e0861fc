<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资金审计看板 Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .container {
            max-width: 1400px;
            margin: 20px auto;
            padding: 0 20px;
        }
        
        .dashboard-tabs {
            display: flex;
            background: white;
            border-radius: 8px 8px 0 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .tab-button {
            flex: 1;
            padding: 15px 20px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }
        
        .tab-button.active {
            background: white;
            border-bottom-color: #667eea;
            color: #667eea;
        }
        
        .tab-button:hover {
            background: #e9ecef;
        }
        
        .dashboard-content {
            background: white;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            min-height: 600px;
        }
        
        .tab-panel {
            display: none;
            padding: 20px;
        }
        
        .tab-panel.active {
            display: block;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .data-table tr:hover {
            background: #f8f9fa;
        }
        
        .tree-node {
            cursor: pointer;
            user-select: none;
        }
        
        .tree-node.expandable::before {
            content: '▶';
            display: inline-block;
            margin-right: 5px;
            transition: transform 0.2s ease;
        }
        
        .tree-node.expanded::before {
            transform: rotate(90deg);
        }
        
        .tree-level-1 { padding-left: 0px; font-weight: 600; }
        .tree-level-2 { padding-left: 20px; font-weight: 500; }
        .tree-level-3 { padding-left: 40px; }
        .tree-level-4 { padding-left: 60px; color: #666; }
        
        .amount {
            text-align: right;
            font-family: 'Consolas', monospace;
            font-weight: 500;
        }
        
        .amount.positive {
            color: #28a745;
        }
        
        .amount.negative {
            color: #dc3545;
        }
        
        .amount.frozen {
            color: #ffc107;
        }
        
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .summary-card h3 {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .summary-card .value {
            font-size: 24px;
            font-weight: 600;
            font-family: 'Consolas', monospace;
        }
        
        .account-type {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .account-type.worker {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .account-type.general {
            background: #f3e5f5;
            color: #7b1fa2;
        }
        
        .search-bar {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .search-input {
            flex: 1;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .search-button {
            padding: 10px 20px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .search-button:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>资金审计管理系统</h1>
        <p>基于施工项目的多维度资金余额监控看板</p>
    </div>
    
    <div class="container">
        <div class="dashboard-tabs">
            <button class="tab-button active" onclick="switchTab(0)">施工管理层级看板</button>
            <button class="tab-button" onclick="switchTab(1)">区域账户分类看板</button>
            <button class="tab-button" onclick="switchTab(2)">银行账户汇总看板</button>
        </div>
        
        <div class="dashboard-content">
            <!-- 看板1：施工管理层级看板 -->
            <div class="tab-panel active" id="panel-0">
                <div class="search-bar">
                    <input type="text" class="search-input" placeholder="搜索施工中心、施工部或项目...">
                    <button class="search-button">搜索</button>
                </div>
                
                <div class="summary-cards">
                    <div class="summary-card">
                        <h3>总制单金额</h3>
                        <div class="value amount frozen">¥2,458,900</div>
                    </div>
                    <div class="summary-card">
                        <h3>总银行余额</h3>
                        <div class="value amount positive">¥8,756,200</div>
                    </div>
                    <div class="summary-card">
                        <h3>总活钱金额</h3>
                        <div class="value amount positive">¥5,234,100</div>
                    </div>
                    <div class="summary-card">
                        <h3>总冻结金额</h3>
                        <div class="value amount frozen">¥1,234,500</div>
                    </div>
                </div>
                
                <table class="data-table">
                    <thead>
                        <tr>
                            <th style="width: 300px;">组织架构</th>
                            <th style="width: 120px;">制单金额</th>
                            <th style="width: 120px;">银行余额</th>
                            <th style="width: 120px;">活钱金额</th>
                            <th style="width: 120px;">冻结金额</th>
                            <th>备注</th>
                        </tr>
                    </thead>
                    <tbody id="hierarchy-table">
                        <!-- 数据将通过JavaScript动态生成 -->
                    </tbody>
                </table>
            </div>
            
            <!-- 看板2：区域账户分类看板 -->
            <div class="tab-panel" id="panel-1">
                <div class="search-bar">
                    <input type="text" class="search-input" placeholder="搜索施工部、片区或账户...">
                    <button class="search-button">搜索</button>
                </div>
                
                <table class="data-table">
                    <thead>
                        <tr>
                            <th style="width: 200px;">施工部</th>
                            <th style="width: 150px;">片区</th>
                            <th style="width: 200px;">银行账户</th>
                            <th style="width: 100px;">账户性质</th>
                            <th style="width: 120px;">制单金额</th>
                            <th style="width: 120px;">银行余额</th>
                            <th style="width: 120px;">活钱金额</th>
                            <th style="width: 120px;">冻结金额</th>
                        </tr>
                    </thead>
                    <tbody id="regional-table">
                        <!-- 数据将通过JavaScript动态生成 -->
                    </tbody>
                </table>
            </div>
            
            <!-- 看板3：银行账户汇总看板 -->
            <div class="tab-panel" id="panel-2">
                <div class="search-bar">
                    <input type="text" class="search-input" placeholder="搜索银行账户...">
                    <button class="search-button">搜索</button>
                </div>
                
                <table class="data-table">
                    <thead>
                        <tr>
                            <th style="width: 200px;">银行名称</th>
                            <th style="width: 250px;">账户号码</th>
                            <th style="width: 100px;">账户性质</th>
                            <th style="width: 120px;">制单金额</th>
                            <th style="width: 120px;">银行余额</th>
                            <th style="width: 120px;">活钱金额</th>
                            <th style="width: 120px;">冻结金额</th>
                            <th>最后更新时间</th>
                        </tr>
                    </thead>
                    <tbody id="bank-table">
                        <!-- 数据将通过JavaScript动态生成 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // 模拟数据
        const mockData = {
            hierarchy: [
                {
                    id: 1,
                    name: '第一施工中心',
                    level: 1,
                    children: [
                        {
                            id: 11,
                            name: '二部',
                            level: 2,
                            children: [
                                {
                                    id: 111,
                                    name: '郦城一期C3（二部）',
                                    level: 3,
                                    amounts: { planned: 458900, bank: 1256200, active: 834100, frozen: 234500 },
                                    children: [
                                        { id: 1111, name: '建设银行-民工专户', level: 4, amounts: { planned: 258900, bank: 756200, active: 0, frozen: 134500 } },
                                        { id: 1112, name: '工商银行-一般户', level: 4, amounts: { planned: 200000, bank: 500000, active: 834100, frozen: 100000 } }
                                    ]
                                },
                                {
                                    id: 112,
                                    name: '西晶项目',
                                    level: 3,
                                    amounts: { planned: 680000, bank: 2100000, active: 1200000, frozen: 300000 },
                                    children: [
                                        { id: 1121, name: '农业银行-民工专户', level: 4, amounts: { planned: 380000, bank: 1200000, active: 0, frozen: 200000 } },
                                        { id: 1122, name: '中国银行-一般户', level: 4, amounts: { planned: 300000, bank: 900000, active: 1200000, frozen: 100000 } }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ],
            regional: [
                { dept: '二部', region: '眉山片区', account: '建设银行-6217***1234', type: 'worker', amounts: { planned: 258900, bank: 756200, active: 0, frozen: 134500 } },
                { dept: '二部', region: '眉山片区', account: '工商银行-6222***5678', type: 'general', amounts: { planned: 200000, bank: 500000, active: 834100, frozen: 100000 } },
                { dept: '三部', region: '成都片区', account: '农业银行-6228***9012', type: 'worker', amounts: { planned: 380000, bank: 1200000, active: 0, frozen: 200000 } }
            ],
            banks: [
                { bank: '中国建设银行', account: '6217001234567890123', type: 'worker', amounts: { planned: 258900, bank: 756200, active: 0, frozen: 134500 }, updateTime: '2024-01-15 14:30:25' },
                { bank: '中国工商银行', account: '6222021234567890456', type: 'general', amounts: { planned: 200000, bank: 500000, active: 834100, frozen: 100000 }, updateTime: '2024-01-15 14:28:15' },
                { bank: '中国农业银行', account: '6228481234567890789', type: 'worker', amounts: { planned: 380000, bank: 1200000, active: 0, frozen: 200000 }, updateTime: '2024-01-15 14:25:42' }
            ]
        };

        // 切换标签页
        function switchTab(index) {
            // 更新标签按钮状态
            document.querySelectorAll('.tab-button').forEach((btn, i) => {
                btn.classList.toggle('active', i === index);
            });
            
            // 更新面板显示状态
            document.querySelectorAll('.tab-panel').forEach((panel, i) => {
                panel.classList.toggle('active', i === index);
            });
        }

        // 格式化金额
        function formatAmount(amount, type = 'default') {
            const formatted = '¥' + amount.toLocaleString();
            let className = 'amount';
            
            if (type === 'frozen' || type === 'planned') {
                className += ' frozen';
            } else if (amount > 0) {
                className += ' positive';
            } else if (amount < 0) {
                className += ' negative';
            }
            
            return `<span class="${className}">${formatted}</span>`;
        }

        // 渲染层级表格
        function renderHierarchyTable() {
            const tbody = document.getElementById('hierarchy-table');
            let html = '';
            
            function renderNode(node, isExpanded = true) {
                const hasChildren = node.children && node.children.length > 0;
                const expandClass = hasChildren ? (isExpanded ? 'expanded' : '') + ' expandable' : '';
                
                html += `
                    <tr class="tree-level-${node.level}" ${hasChildren ? `onclick="toggleNode(this)"` : ''}>
                        <td class="tree-node ${expandClass}">${node.name}</td>
                        <td>${node.amounts ? formatAmount(node.amounts.planned, 'planned') : ''}</td>
                        <td>${node.amounts ? formatAmount(node.amounts.bank) : ''}</td>
                        <td>${node.amounts ? formatAmount(node.amounts.active) : ''}</td>
                        <td>${node.amounts ? formatAmount(node.amounts.frozen, 'frozen') : ''}</td>
                        <td>${node.level === 4 ? '银行账户' : node.level === 3 ? '项目' : node.level === 2 ? '施工部' : '施工中心'}</td>
                    </tr>
                `;
                
                if (hasChildren && isExpanded) {
                    node.children.forEach(child => renderNode(child, true));
                }
            }
            
            mockData.hierarchy.forEach(node => renderNode(node, true));
            tbody.innerHTML = html;
        }

        // 渲染区域表格
        function renderRegionalTable() {
            const tbody = document.getElementById('regional-table');
            let html = '';
            
            mockData.regional.forEach(item => {
                html += `
                    <tr>
                        <td>${item.dept}</td>
                        <td>${item.region}</td>
                        <td>${item.account}</td>
                        <td><span class="account-type ${item.type}">${item.type === 'worker' ? '民工专户' : '一般户'}</span></td>
                        <td>${formatAmount(item.amounts.planned, 'planned')}</td>
                        <td>${formatAmount(item.amounts.bank)}</td>
                        <td>${formatAmount(item.amounts.active)}</td>
                        <td>${formatAmount(item.amounts.frozen, 'frozen')}</td>
                    </tr>
                `;
            });
            
            tbody.innerHTML = html;
        }

        // 渲染银行表格
        function renderBankTable() {
            const tbody = document.getElementById('bank-table');
            let html = '';
            
            mockData.banks.forEach(item => {
                html += `
                    <tr>
                        <td>${item.bank}</td>
                        <td>${item.account}</td>
                        <td><span class="account-type ${item.type}">${item.type === 'worker' ? '民工专户' : '一般户'}</span></td>
                        <td>${formatAmount(item.amounts.planned, 'planned')}</td>
                        <td>${formatAmount(item.amounts.bank)}</td>
                        <td>${formatAmount(item.amounts.active)}</td>
                        <td>${formatAmount(item.amounts.frozen, 'frozen')}</td>
                        <td>${item.updateTime}</td>
                    </tr>
                `;
            });
            
            tbody.innerHTML = html;
        }

        // 切换节点展开/折叠
        function toggleNode(row) {
            const node = row.querySelector('.tree-node');
            if (!node.classList.contains('expandable')) return;
            
            const isExpanded = node.classList.contains('expanded');
            node.classList.toggle('expanded');
            
            // 这里应该重新渲染表格，简化处理
            console.log('Toggle node:', isExpanded ? 'collapse' : 'expand');
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            renderHierarchyTable();
            renderRegionalTable();
            renderBankTable();
        });
    </script>
</body>
</html>
