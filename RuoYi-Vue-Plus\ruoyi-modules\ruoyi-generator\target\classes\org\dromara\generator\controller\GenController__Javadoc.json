{"doc": "\n 代码生成 操作处理\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "genList", "paramTypes": ["org.dromara.generator.domain.GenTable", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询代码生成列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 修改代码生成业务\r\n\r\n @param tableId 表ID\r\n"}, {"name": "dataList", "paramTypes": ["org.dromara.generator.domain.GenTable", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询数据库列表\r\n"}, {"name": "columnList", "paramTypes": ["java.lang.Long"], "doc": "\n 查询数据表字段列表\r\n\r\n @param tableId 表ID\r\n"}, {"name": "importTableSave", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 导入表结构（保存）\r\n\r\n @param tables 表名串\r\n"}, {"name": "editSave", "paramTypes": ["org.dromara.generator.domain.GenTable"], "doc": "\n 修改保存代码生成业务\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除代码生成\r\n\r\n @param tableIds 表ID串\r\n"}, {"name": "preview", "paramTypes": ["java.lang.Long"], "doc": "\n 预览代码\r\n\r\n @param tableId 表ID\r\n"}, {"name": "download", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "java.lang.Long"], "doc": "\n 生成代码（下载方式）\r\n\r\n @param tableId 表ID\r\n"}, {"name": "genCode", "paramTypes": ["java.lang.Long"], "doc": "\n 生成代码（自定义路径）\r\n\r\n @param tableId 表ID\r\n"}, {"name": "synchDb", "paramTypes": ["java.lang.Long"], "doc": "\n 同步数据库\r\n\r\n @param tableId 表ID\r\n"}, {"name": "batchGenCode", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "java.lang.String"], "doc": "\n 批量生成代码\r\n\r\n @param tableIdStr 表ID串\r\n"}, {"name": "genCode", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "byte[]"], "doc": "\n 生成zip文件\r\n"}, {"name": "getCurrentDataSourceNameList", "paramTypes": [], "doc": "\n 查询数据源名称列表\r\n"}], "constructors": []}