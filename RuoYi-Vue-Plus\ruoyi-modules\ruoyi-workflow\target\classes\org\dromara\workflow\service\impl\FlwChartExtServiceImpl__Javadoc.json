{"doc": "\n 流程图提示信息\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "execute", "paramTypes": ["org.dromara.warm.flow.core.dto.DefJson"], "doc": "\n 设置流程图提示信息\r\n\r\n @param defJson 流程定义json对象\r\n"}, {"name": "initPrompt<PERSON><PERSON>nt", "paramTypes": ["org.dromara.warm.flow.core.dto.DefJson"], "doc": "\n 初始化流程图提示信息\r\n\r\n @param defJson 流程定义json对象\r\n"}, {"name": "processNodeExtInfo", "paramTypes": ["org.dromara.warm.flow.core.dto.NodeJson", "java.util.List", "java.util.Map", "java.util.Map"], "doc": "\n 处理节点的扩展信息，构建用于流程图悬浮提示的内容\r\n\r\n @param nodeJson 当前节点对象\r\n @param taskList 当前节点对应的历史审批任务列表\r\n"}, {"name": "buildInfoItem", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 构建单条提示内容对象 InfoItem，用于悬浮窗显示（key: value）\r\n\r\n @param key   字段名（作为前缀）\r\n @param value 字段值\r\n @return 提示项对象\r\n"}, {"name": "getHisTaskGroupedByNode", "paramTypes": ["java.lang.Long"], "doc": "\n 根据流程实例ID获取历史任务列表\r\n\r\n @param instanceId 流程实例ID\r\n @return 历史任务列表\r\n"}], "constructors": []}