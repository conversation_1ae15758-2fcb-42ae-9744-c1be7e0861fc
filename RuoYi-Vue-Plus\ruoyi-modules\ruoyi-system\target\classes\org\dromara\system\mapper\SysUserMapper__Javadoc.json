{"doc": "\n 用户表 数据层\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectPageUserList", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": "\n 分页查询用户列表，并进行数据权限控制\r\n\r\n @param page         分页参数\r\n @param queryWrapper 查询条件\r\n @return 分页的用户信息\r\n"}, {"name": "selectUserList", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": "\n 查询用户列表，并进行数据权限控制\r\n\r\n @param queryWrapper 查询条件\r\n @return 用户信息集合\r\n"}, {"name": "selectUserExportList", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": "\n 根据条件分页查询用户列表\r\n\r\n @param queryWrapper 查询条件\r\n @return 用户信息集合信息\r\n"}, {"name": "selectAllocatedList", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": "\n 根据条件分页查询已配用户角色列表\r\n\r\n @param page         分页信息\r\n @param queryWrapper 查询条件\r\n @return 用户信息集合信息\r\n"}, {"name": "selectUnallocatedList", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": "\n 根据条件分页查询未分配用户角色列表\r\n\r\n @param queryWrapper 查询条件\r\n @return 用户信息集合信息\r\n"}, {"name": "countUserById", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID统计用户数量\r\n\r\n @param userId 用户ID\r\n @return 用户数量\r\n"}, {"name": "update", "paramTypes": ["org.dromara.system.domain.SysUser", "com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": "\n 根据条件更新用户数据\r\n\r\n @param user          要更新的用户实体\r\n @param updateWrapper 更新条件封装器\r\n @return 更新操作影响的行数\r\n"}, {"name": "updateById", "paramTypes": ["org.dromara.system.domain.SysUser"], "doc": "\n 根据用户ID更新用户数据\r\n\r\n @param user 要更新的用户实体\r\n @return 更新操作影响的行数\r\n"}], "constructors": []}