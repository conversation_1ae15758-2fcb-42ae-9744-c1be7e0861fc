{"doc": "\n 社会化关系Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2023-06-12\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.String"], "doc": "\n 查询社会化关系\r\n"}, {"name": "queryList", "paramTypes": ["org.dromara.system.domain.bo.SysSocialBo"], "doc": "\n 授权列表\r\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.system.domain.bo.SysSocialBo"], "doc": "\n 新增社会化关系\r\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.system.domain.bo.SysSocialBo"], "doc": "\n 更新社会化关系\r\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.system.domain.SysSocial"], "doc": "\n 保存前的数据校验\r\n"}, {"name": "deleteWithValidById", "paramTypes": ["java.lang.Long"], "doc": "\n 删除社会化关系\r\n"}, {"name": "selectByAuthId", "paramTypes": ["java.lang.String"], "doc": "\n 根据 authId 查询用户信息\r\n\r\n @param authId 认证id\r\n @return 授权信息\r\n"}], "constructors": []}