{"doc": "\n WebSocketHandler 实现类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "afterConnectionEstablished", "paramTypes": ["org.springframework.web.socket.WebSocketSession"], "doc": "\n 连接成功后\r\n"}, {"name": "handleTextMessage", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "org.springframework.web.socket.TextMessage"], "doc": "\n 处理接收到的文本消息\r\n\r\n @param session WebSocket会话\r\n @param message 接收到的文本消息\r\n @throws Exception 处理消息过程中可能抛出的异常\r\n"}, {"name": "handleBinaryMessage", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "org.springframework.web.socket.BinaryMessage"], "doc": "\n 处理接收到的二进制消息\r\n\r\n @param session WebSocket会话\r\n @param message 接收到的二进制消息\r\n @throws Exception 处理消息过程中可能抛出的异常\r\n"}, {"name": "handlePongMessage", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "org.springframework.web.socket.PongMessage"], "doc": "\n 处理接收到的Pong消息（心跳监测）\r\n\r\n @param session WebSocket会话\r\n @param message 接收到的Pong消息\r\n @throws Exception 处理消息过程中可能抛出的异常\r\n"}, {"name": "handleTransportError", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "java.lang.Throwable"], "doc": "\n 处理WebSocket传输错误\r\n\r\n @param session   WebSocket会话\r\n @param exception 发生的异常\r\n @throws Exception 处理过程中可能抛出的异常\r\n"}, {"name": "afterConnectionClosed", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "org.springframework.web.socket.CloseStatus"], "doc": "\n 在WebSocket连接关闭后执行清理操作\r\n\r\n @param session WebSocket会话\r\n @param status  关闭状态信息\r\n"}, {"name": "supportsPartialMessages", "paramTypes": [], "doc": "\n 指示处理程序是否支持接收部分消息\r\n\r\n @return 如果支持接收部分消息，则返回true；否则返回false\r\n"}], "constructors": []}