{"doc": "\n anyline 适配 动态数据源改造\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "feature", "paramTypes": ["org.anyline.data.runtime.DataRuntime", "java.lang.Object"], "doc": "\n 数据源特征 用来定准 adapter 包含数据库或JDBC协议关键字<br/>\r\n 一般会通过 产品名_url 合成 如果返回null 上层方法会通过driver_产品名_url合成\r\n\r\n @param datasource 数据源\r\n @return String 返回null由上层自动提取\r\n"}, {"name": "key", "paramTypes": ["org.anyline.data.runtime.DataRuntime", "java.lang.Object"], "doc": "\n 数据源唯一标识 如果不实现则默认feature\r\n @param datasource 数据源\r\n @return String 返回null由上层自动提取\r\n"}, {"name": "keepAdapter", "paramTypes": ["org.anyline.data.runtime.DataRuntime", "java.lang.Object"], "doc": "\n ConfigTable.KEEP_ADAPTER=2 : 根据当前接口判断是否保持同一个数据源绑定同一个adapter<br/>\r\n DynamicRoutingDataSource类型的返回false,因为同一个DynamicRoutingDataSource可能对应多类数据库, 如果项目中只有一种数据库 应该直接返回true\r\n\r\n @param datasource 数据源\r\n @return boolean\r\n"}], "constructors": []}