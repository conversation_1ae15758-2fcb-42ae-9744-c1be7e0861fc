{"doc": "\n 流程实例 服务层\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectRunningInstanceList", "paramTypes": ["org.dromara.workflow.domain.bo.FlowInstanceBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 分页查询正在运行的流程实例\r\n\r\n @param flowInstanceBo 流程实例\r\n @param pageQuery      分页\r\n @return 结果\r\n"}, {"name": "selectFinishInstanceList", "paramTypes": ["org.dromara.workflow.domain.bo.FlowInstanceBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 分页查询已结束的流程实例\r\n\r\n @param flowInstanceBo 流程实例\r\n @param pageQuery      分页\r\n @return 结果\r\n"}, {"name": "queryByBusinessId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据业务id查询流程实例详细信息\r\n\r\n @param businessId 业务id\r\n @return 结果\r\n"}, {"name": "selectInstByBusinessId", "paramTypes": ["java.lang.String"], "doc": "\n 按照业务id查询流程实例\r\n\r\n @param businessId 业务id\r\n @return 结果\r\n"}, {"name": "selectInstById", "paramTypes": ["java.lang.Long"], "doc": "\n 按照实例id查询流程实例\r\n\r\n @param instanceId 实例id\r\n @return 结果\r\n"}, {"name": "selectInstListByIdList", "paramTypes": ["java.util.List"], "doc": "\n 按照实例id查询流程实例\r\n\r\n @param instanceIds 实例id\r\n @return 结果\r\n"}, {"name": "deleteByBusinessIds", "paramTypes": ["java.util.List"], "doc": "\n 按照业务id删除流程实例\r\n\r\n @param businessIds 业务id\r\n @return 结果\r\n"}, {"name": "deleteByInstanceIds", "paramTypes": ["java.util.List"], "doc": "\n 按照实例id删除流程实例\r\n\r\n @param instanceIds 实例id\r\n @return 结果\r\n"}, {"name": "cancelProcessApply", "paramTypes": ["org.dromara.workflow.domain.bo.FlowCancelBo"], "doc": "\n 撤销流程\r\n\r\n @param bo 参数\r\n @return 结果\r\n"}, {"name": "selectCurrentInstanceList", "paramTypes": ["org.dromara.workflow.domain.bo.FlowInstanceBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取当前登陆人发起的流程实例\r\n\r\n @param instanceBo 流程实例\r\n @param pageQuery  分页\r\n @return 结果\r\n"}, {"name": "flowHisTaskList", "paramTypes": ["java.lang.String"], "doc": "\n 获取流程图,流程记录\r\n\r\n @param businessId 业务id\r\n @return 结果\r\n"}, {"name": "updateStatus", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 按照实例id更新状态\r\n\r\n @param instanceId 实例id\r\n @param status     状态\r\n"}, {"name": "instanceVariable", "paramTypes": ["java.lang.Long"], "doc": "\n 获取流程变量\r\n\r\n @param instanceId 实例id\r\n @return 结果\r\n"}, {"name": "setVariable", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": "\n 设置流程变量\r\n\r\n @param instanceId 实例id\r\n @param variable   流程变量\r\n"}, {"name": "selectByTaskId", "paramTypes": ["java.lang.Long"], "doc": "\n 按任务id查询实例\r\n\r\n @param taskId 任务id\r\n @return 结果\r\n"}, {"name": "selectByTaskIdList", "paramTypes": ["java.util.List"], "doc": "\n 按任务id查询实例\r\n\r\n @param taskIdList 任务id\r\n @return 结果\r\n"}, {"name": "processInvalid", "paramTypes": ["org.dromara.workflow.domain.bo.FlowInvalidBo"], "doc": "\n 作废流程\r\n\r\n @param bo 流程实例\r\n @return 结果\r\n"}], "constructors": []}