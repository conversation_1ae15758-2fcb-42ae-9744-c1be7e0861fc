{"doc": "\n 测试单表视图对象 test_demo\r\n\r\n <AUTHOR>\r\n @date 2021-07-26\r\n", "fields": [{"name": "id", "doc": "\n 主键\r\n"}, {"name": "deptId", "doc": "\n 部门id\r\n"}, {"name": "userId", "doc": "\n 用户id\r\n"}, {"name": "orderNum", "doc": "\n 排序号\r\n"}, {"name": "<PERSON><PERSON><PERSON>", "doc": "\n key键\r\n"}, {"name": "value", "doc": "\n 值\r\n"}, {"name": "createTime", "doc": "\n 创建时间\r\n"}, {"name": "createBy", "doc": "\n 创建人\r\n"}, {"name": "createByName", "doc": "\n 创建人账号\r\n"}, {"name": "updateTime", "doc": "\n 更新时间\r\n"}, {"name": "updateBy", "doc": "\n 更新人\r\n"}, {"name": "updateByName", "doc": "\n 更新人账号\r\n"}, {"name": "version", "doc": "\n 版本\r\n"}], "enumConstants": [], "methods": [], "constructors": []}