{"doc": "\n 用户对象导出VO\r\n\r\n <AUTHOR> Li\r\n", "fields": [{"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "userName", "doc": "\n 用户账号\r\n"}, {"name": "nick<PERSON><PERSON>", "doc": "\n 用户昵称\r\n"}, {"name": "email", "doc": "\n 用户邮箱\r\n"}, {"name": "phonenumber", "doc": "\n 手机号码\r\n"}, {"name": "sex", "doc": "\n 用户性别\r\n"}, {"name": "status", "doc": "\n 帐号状态（0正常 1停用）\r\n"}, {"name": "loginIp", "doc": "\n 最后登录IP\r\n"}, {"name": "loginDate", "doc": "\n 最后登录时间\r\n"}, {"name": "deptName", "doc": "\n 部门名称\r\n"}, {"name": "leader<PERSON><PERSON>", "doc": "\n 负责人\r\n"}], "enumConstants": [], "methods": [], "constructors": []}