{"doc": "\n 用户对象 sys_user\r\n\r\n <AUTHOR> Li\r\n", "fields": [{"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "deptId", "doc": "\n 部门ID\r\n"}, {"name": "userName", "doc": "\n 用户账号\r\n"}, {"name": "nick<PERSON><PERSON>", "doc": "\n 用户昵称\r\n"}, {"name": "userType", "doc": "\n 用户类型（sys_user系统用户）\r\n"}, {"name": "email", "doc": "\n 用户邮箱\r\n"}, {"name": "phonenumber", "doc": "\n 手机号码\r\n"}, {"name": "sex", "doc": "\n 用户性别\r\n"}, {"name": "avatar", "doc": "\n 用户头像\r\n"}, {"name": "password", "doc": "\n 密码\r\n"}, {"name": "status", "doc": "\n 帐号状态（0正常 1停用）\r\n"}, {"name": "delFlag", "doc": "\n 删除标志（0代表存在 1代表删除）\r\n"}, {"name": "loginIp", "doc": "\n 最后登录IP\r\n"}, {"name": "loginDate", "doc": "\n 最后登录时间\r\n"}, {"name": "remark", "doc": "\n 备注\r\n"}], "enumConstants": [], "methods": [], "constructors": []}