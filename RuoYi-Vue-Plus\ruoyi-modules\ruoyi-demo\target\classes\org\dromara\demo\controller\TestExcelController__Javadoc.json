{"doc": "\n 测试Excel功能\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "exportTemplateOne", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": "\n 单列表多数据\r\n"}, {"name": "exportTemplateMuliti", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": "\n 多列表多数据\r\n"}, {"name": "exportWithOptions", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": "\n 导出下拉框\r\n\r\n @param response /\r\n"}, {"name": "exportTemplateMultiSheet", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": "\n 多个sheet导出\r\n"}, {"name": "importWithOptions", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": "\n 导入表格\r\n"}], "constructors": []}