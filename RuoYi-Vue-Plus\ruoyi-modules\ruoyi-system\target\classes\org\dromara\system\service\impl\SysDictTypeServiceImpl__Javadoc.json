{"doc": "\n 字典 业务层处理\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectDictTypeList", "paramTypes": ["org.dromara.system.domain.bo.SysDictTypeBo"], "doc": "\n 根据条件分页查询字典类型\r\n\r\n @param dictType 字典类型信息\r\n @return 字典类型集合信息\r\n"}, {"name": "selectDictTypeAll", "paramTypes": [], "doc": "\n 根据所有字典类型\r\n\r\n @return 字典类型集合信息\r\n"}, {"name": "selectDictDataByType", "paramTypes": ["java.lang.String"], "doc": "\n 根据字典类型查询字典数据\r\n\r\n @param dictType 字典类型\r\n @return 字典数据集合信息\r\n"}, {"name": "selectDictTypeById", "paramTypes": ["java.lang.Long"], "doc": "\n 根据字典类型ID查询信息\r\n\r\n @param dictId 字典类型ID\r\n @return 字典类型\r\n"}, {"name": "selectDictTypeByType", "paramTypes": ["java.lang.String"], "doc": "\n 根据字典类型查询信息\r\n\r\n @param dictType 字典类型\r\n @return 字典类型\r\n"}, {"name": "deleteDictTypeByIds", "paramTypes": ["java.util.List"], "doc": "\n 批量删除字典类型信息\r\n\r\n @param dictIds 需要删除的字典ID\r\n"}, {"name": "resetDictCache", "paramTypes": [], "doc": "\n 重置字典缓存数据\r\n"}, {"name": "insertDictType", "paramTypes": ["org.dromara.system.domain.bo.SysDictTypeBo"], "doc": "\n 新增保存字典类型信息\r\n\r\n @param bo 字典类型信息\r\n @return 结果\r\n"}, {"name": "updateDictType", "paramTypes": ["org.dromara.system.domain.bo.SysDictTypeBo"], "doc": "\n 修改保存字典类型信息\r\n\r\n @param bo 字典类型信息\r\n @return 结果\r\n"}, {"name": "checkDictTypeUnique", "paramTypes": ["org.dromara.system.domain.bo.SysDictTypeBo"], "doc": "\n 校验字典类型称是否唯一\r\n\r\n @param dictType 字典类型\r\n @return 结果\r\n"}, {"name": "getDictLabel", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 根据字典类型和字典值获取字典标签\r\n\r\n @param dictType  字典类型\r\n @param dictValue 字典值\r\n @param separator 分隔符\r\n @return 字典标签\r\n"}, {"name": "getDictValue", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 根据字典类型和字典标签获取字典值\r\n\r\n @param dictType  字典类型\r\n @param dictLabel 字典标签\r\n @param separator 分隔符\r\n @return 字典值\r\n"}, {"name": "getAllDictByDictType", "paramTypes": ["java.lang.String"], "doc": "\n 获取字典下所有的字典值与标签\r\n\r\n @param dictType 字典类型\r\n @return dictValue为key，dictLabel为值组成的Map\r\n"}, {"name": "getDictType", "paramTypes": ["java.lang.String"], "doc": "\n 根据字典类型查询详细信息\r\n\r\n @param dictType 字典类型\r\n @return 字典类型详细信息\r\n"}, {"name": "getDictData", "paramTypes": ["java.lang.String"], "doc": "\n 根据字典类型查询字典数据列表\r\n\r\n @param dictType 字典类型\r\n @return 字典数据列表\r\n"}], "constructors": []}