{"doc": "\n 租户业务对象 sys_tenant\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "id", "doc": "\n id\r\n"}, {"name": "tenantId", "doc": "\n 租户编号\r\n"}, {"name": "contactUserName", "doc": "\n 联系人\r\n"}, {"name": "contactPhone", "doc": "\n 联系电话\r\n"}, {"name": "companyName", "doc": "\n 企业名称\r\n"}, {"name": "username", "doc": "\n 用户名（创建系统用户）\r\n"}, {"name": "password", "doc": "\n 密码（创建系统用户）\r\n"}, {"name": "licenseNumber", "doc": "\n 统一社会信用代码\r\n"}, {"name": "address", "doc": "\n 地址\r\n"}, {"name": "domain", "doc": "\n 域名\r\n"}, {"name": "intro", "doc": "\n 企业简介\r\n"}, {"name": "remark", "doc": "\n 备注\r\n"}, {"name": "packageId", "doc": "\n 租户套餐编号\r\n"}, {"name": "expireTime", "doc": "\n 过期时间\r\n"}, {"name": "accountCount", "doc": "\n 用户数量（-1不限制）\r\n"}, {"name": "status", "doc": "\n 租户状态（0正常 1停用）\r\n"}], "enumConstants": [], "methods": [], "constructors": []}